import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { mockDashboardData, mockSession } from "../../../../lib/mockData";
import {
  DashboardGoal,
  TimeRange,
  type DashboardMetric,
} from "../_lib/constants";

interface UseDashboardProps {
  alertInfos: any[];
  alertInfosLoading?: boolean;
  storeId?: string;
}

// Define the type locally since we're not using services
type ChargebackRateData = {
  date: string;
  rate: number;
  disputeCount: number;
  transactionCount: number;
};

interface UseDashboardReturn {
  timeRange: TimeRange;
  customDateRange?: { from: Date; to: Date };
  metrics: DashboardMetric[] | null;
  goal: DashboardGoal | null;
  chargebackRateData: ChargebackRateData[];
  loading: boolean;
  metricsLoading: boolean;
  chartLoading: boolean;
  error: string | null;
  lastRefresh: Date | null;
  // Actions
  setTimeRange: (
    timeRange: TimeRange,
    dateRange?: { from: Date; to: Date }
  ) => void;
  refetch: () => Promise<void>;
}

// Mock response validation - always returns true for mock data
function isValidMockResponse(response: any): boolean {
  return response && typeof response === "object";
}

export function useDashboard({
  alertInfos,
  alertInfosLoading = false,
  storeId,
}: UseDashboardProps): UseDashboardReturn {
  // Using mock data instead of session
  // State
  const [timeRange, setTimeRangeState] = useState<TimeRange>(
    TimeRange.ALL_TIME
  );
  const [customDateRange, setCustomDateRange] = useState<
    { from: Date; to: Date } | undefined
  >();
  const [metrics, setMetrics] = useState<DashboardMetric[]>([]);
  const [goal, setGoal] = useState<DashboardGoal | null>(null);
  const [chargebackRateData, setChargebackRateData] = useState<
    ChargebackRateData[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [metricsLoading, setMetricsLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Use refs to track ongoing requests and prevent duplicates
  const fetchingOverview = useRef(false);
  const fetchingChart = useRef(false);

  // Memoize alert info filters separately for better performance
  const alertInfoFilters = useMemo(() => {
    if (!alertInfos || alertInfos.length === 0) return {};

    const ethocaInfos = alertInfos.filter(
      (info) =>
        info.alertType === "ETHOCA" && info.registrationStatus === "EFFECTED"
    );
    const rdrInfos = alertInfos.filter(
      (info) =>
        info.alertType === "RDR" && info.registrationStatus === "EFFECTED"
    );
    const effectiveInfos = alertInfos.filter(
      (info) => info.registrationStatus === "EFFECTED"
    );

    const filters: any = {};

    // Remove ETHOCA-specific descriptor filtering - no longer needed
    const descriptors = [
      ...new Set(ethocaInfos.map((info) => info.descriptor).filter(Boolean)),
    ];
    if (descriptors.length > 0) filters.descriptor = descriptors;

    // Include all alert types without filtering by type
    // const types = [...new Set(effectiveInfos.map(info => info.alertType).filter(Boolean))];
    // if (types.length > 0) filters.type = types;

    const bins = [...new Set(rdrInfos.map((info) => info.bin).filter(Boolean))];
    if (bins.length > 0) filters.cardBin = bins;

    const caids = [
      ...new Set(rdrInfos.map((info) => info.caid).filter(Boolean)),
    ];
    if (caids.length > 0) filters.caid = caids;
    return filters;
  }, [alertInfos]);

  // Memoize filters to prevent recalculation on every render
  const memoizedFilters = useMemo(() => {
    const filters: any = {
      groupBy: "overview",
      timeRange: timeRange,
      viewMode: "dashboard",
      linkedStoreId: mockSession.user?.id,
      ...alertInfoFilters,
    };

    // Add custom date range as separate ISO strings if it's selected
    if (timeRange === TimeRange.CUSTOM && customDateRange) {
      filters.startDate = customDateRange.from.toISOString();
      filters.endDate = customDateRange.to.toISOString();
    }

    return filters;
  }, [timeRange, customDateRange, mockSession.user?.id, alertInfoFilters]);

  // Memoize chart filters separately for chart data
  const memoizedChartFilters = useMemo(() => {
    const filters: Record<string, any> = {
      linkedStoreId: mockSession.user?.id,
      timeRange: timeRange,
      ...alertInfoFilters,
    };

    // Add custom date range if applicable
    if (
      timeRange === TimeRange.CUSTOM &&
      customDateRange?.from &&
      customDateRange?.to
    ) {
      filters.startDate = customDateRange.from.toISOString();
      filters.endDate = customDateRange.to.toISOString();
    }

    return filters;
  }, [timeRange, customDateRange, mockSession.user?.id, alertInfoFilters]);

  // Mock fetch chargeback rate data
  const fetchChargebackRateData = useCallback(async () => {
    if (fetchingChart.current) return;

    try {
      fetchingChart.current = true;
      setChartLoading(true);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Get mock data based on time range
      let mockData;
      switch (timeRange) {
        case TimeRange.THIS_MONTH:
          mockData = mockDashboardData.chartData.month;
          break;
        case TimeRange.THIS_YEAR:
          mockData = mockDashboardData.chartData.year;
          break;
        case TimeRange.ALL_TIME:
        default:
          mockData = mockDashboardData.chartData.allTime;
          break;
      }

      setChargebackRateData(mockData);
    } catch (error) {
      setChargebackRateData([]);
    } finally {
      setChartLoading(false);
      fetchingChart.current = false;
    }
  }, [timeRange]);

  // Mock fetch overview data
  const fetchOverview = useCallback(async () => {
    if (fetchingOverview.current) return;

    try {
      fetchingOverview.current = true;
      setMetricsLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Use mock dashboard data
      const overview = mockDashboardData.overview;

      // Create metrics from mock data
      const metricsData: DashboardMetric[] = [
        {
          id: "1",
          label: "Alerts",
          value: overview.blocks.count,
          format: "number",
        },
        {
          id: "2",
          label: "Blocked",
          value: overview.blocks.blocked,
          format: "number",
        },
        {
          id: "3",
          label: "Chargeback Rate",
          value: parseFloat(overview.rate.actualChargeback),
          format: "percentage",
        },
        {
          id: "4",
          label: "Chargeback Rate (Projected)",
          value: parseFloat(overview.rate.blocked),
          format: "percentage",
        },
      ];

      // Set goal from mock data
      setGoal({
        id: "1",
        label: "Profit Goal",
        actualChargeback: {
          value: parseFloat(overview.goal.actualChargeback),
          format: "percentage",
        },
        chargebackGoal: {
          value: parseFloat(overview.goal.chargebackGoal),
          format: "percentage",
        },
        profitGoalProgress: {
          value: parseFloat(overview.goal.profitGoalProgress),
          format: "percentage",
        },
      });

      setMetrics(metricsData);

      setLastRefresh(new Date());
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch dashboard data"
      );
    } finally {
      setMetricsLoading(false);
      fetchingOverview.current = false;
    }
  }, [storeId]);

  // Refetch function - runs both metrics and chart in parallel
  const refetch = useCallback(async () => {
    setLoading(true);
    await Promise.all([fetchOverview(), fetchChargebackRateData()]);
    setLoading(false);
  }, [fetchOverview, fetchChargebackRateData]);

  // Track if initial load is complete
  const initialLoadComplete = useRef(false);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    if (!alertInfosLoading && storeId && mockSession?.user?.access_token) {
      // Skip if already fetching
      if (fetchingOverview.current || fetchingChart.current) {
        return;
      }
      // Run overview and chart data fetch in parallel for faster loading
      Promise.all([fetchOverview(), fetchChargebackRateData()]).finally(() => {
        setLoading(false);
        initialLoadComplete.current = true;
      });
    }
  }, [
    timeRange,
    alertInfosLoading,
    storeId,
    mockSession.user?.access_token,
    memoizedFilters.timeRange,
    memoizedChartFilters.timeRange,
  ]);

  // Handle time range change
  const setTimeRange = useCallback(
    (newTimeRange: TimeRange, dateRange?: { from: Date; to: Date }) => {
      setTimeRangeState(newTimeRange);
      if (newTimeRange === TimeRange.CUSTOM && dateRange) {
        setCustomDateRange(dateRange);
      } else if (newTimeRange !== TimeRange.CUSTOM) {
        setCustomDateRange(undefined);
      }
    },
    []
  );

  return {
    // State
    timeRange,
    customDateRange,
    metrics,
    goal,
    chargebackRateData,
    loading: loading || metricsLoading, // Combined loading state
    metricsLoading,
    chartLoading,
    error,
    lastRefresh,

    // Actions
    setTimeRange,
    refetch,
  };
}
