import { useMemo } from "react";
import {
  mockSelectedStore,
  mockAlertInfo,
  mockLoadingStates,
} from "../../../../lib/mockData";

// Mock optimized dashboard selectors
export function useOptimizedDashboardSelectors() {
  const store = mockSelectedStore;

  const dashboardData = useMemo(
    () => ({
      store: mockSelectedStore,
      alertInfosLoading: mockLoadingStates.alerts,
      alertInfos: mockAlertInfo,
    }),
    []
  );

  return useMemo(
    () => dashboardData,
    [
      dashboardData.store?.id,
      dashboardData.alertInfosLoading,
      // Only re-render if alertInfos length changes or items change
      dashboardData.alertInfos?.length,
      JSON.stringify(dashboardData.alertInfos?.map((info) => info.id)),
    ]
  );
}
