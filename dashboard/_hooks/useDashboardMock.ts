import { useState, useEffect, useCallback, useMemo } from "react";
import { mockDashboardData } from "../../../../lib/mockData";
import {
  DashboardGoal,
  TimeRange,
  type DashboardMetric,
} from "../_lib/constants";

interface UseDashboardProps {
  alertInfos: any[];
  alertInfosLoading?: boolean;
  storeId?: string;
}

type ChargebackRateData = {
  date: string;
  rate: number;
  disputeCount: number;
  transactionCount: number;
};

interface UseDashboardReturn {
  timeRange: TimeRange;
  customDateRange?: { from: Date; to: Date };
  metrics: DashboardMetric[] | null;
  goal: DashboardGoal | null;
  chargebackRateData: ChargebackRateData[];
  loading: boolean;
  metricsLoading: boolean;
  chartLoading: boolean;
  error: string | null;
  lastRefresh: Date | null;
  // Actions
  setTimeRange: (
    timeRange: TimeRange,
    dateRange?: { from: Date; to: Date }
  ) => Promise<void>;
  refetch: () => Promise<void>;
}

export function useDashboardMock({
  alertInfos,
  alertInfosLoading = false,
  storeId,
}: UseDashboardProps): UseDashboardReturn {
  // State
  const [timeRange, setTimeRangeState] = useState<TimeRange>(
    TimeRange.ALL_TIME
  );
  const [customDateRange, setCustomDateRange] = useState<
    { from: Date; to: Date } | undefined
  >();
  const [loading, setLoading] = useState(false);
  const [metricsLoading, setMetricsLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Get mock data based on time range
  const getChartDataForTimeRange = useCallback(() => {
    switch (timeRange) {
      case TimeRange.THIS_MONTH:
        return mockDashboardData.chartData.month;
      case TimeRange.THIS_YEAR:
        return mockDashboardData.chartData.year;
      case TimeRange.ALL_TIME:
      default:
        return mockDashboardData.chartData.allTime;
    }
  }, [timeRange]);

  // Mock metrics based on dashboard data and time range
  const metrics = useMemo<DashboardMetric[]>(() => {
    // Generate different metrics based on time range
    let alertsCount: number;
    let blockedCount: number;
    let chargebackRate: number;
    let projectedRate: number;

    switch (timeRange) {
      case TimeRange.THIS_MONTH:
        // Current month metrics - smaller numbers
        alertsCount = 125;
        blockedCount = 88;
        chargebackRate = 0.15;
        projectedRate = 0.08;
        break;
      case TimeRange.THIS_YEAR:
        // Year to date metrics - medium numbers
        alertsCount = 820;
        blockedCount = 574;
        chargebackRate = 0.13;
        projectedRate = 0.06;
        break;
      case TimeRange.ALL_TIME:
      default:
        // All time metrics - use original mock data
        const overview = mockDashboardData.overview;
        alertsCount = overview.blocks.count;
        blockedCount = overview.blocks.blocked;
        chargebackRate = parseFloat(overview.rate.actualChargeback);
        projectedRate = parseFloat(overview.rate.blocked);
        break;
    }

    return [
      {
        id: "1",
        label: "Alerts",
        value: alertsCount,
        format: "number",
      },
      {
        id: "2",
        label: "Blocked",
        value: blockedCount,
        format: "number",
      },
      {
        id: "3",
        label: "Chargeback Rate",
        value: chargebackRate,
        format: "percentage",
      },
      {
        id: "4",
        label: "Chargeback Rate (Projected)",
        value: projectedRate,
        format: "percentage",
      },
    ];
  }, [timeRange]);

  // Mock goal based on time range
  const goal = useMemo<DashboardGoal>(() => {
    let actualChargebackValue: number;
    let chargebackGoalValue: number;
    let profitGoalProgressValue: number;

    switch (timeRange) {
      case TimeRange.THIS_MONTH:
        // Current month goal metrics
        actualChargebackValue = 0.15;
        chargebackGoalValue = 1.00;
        profitGoalProgressValue = 85.00;
        break;
      case TimeRange.THIS_YEAR:
        // Year to date goal metrics
        actualChargebackValue = 0.13;
        chargebackGoalValue = 1.00;
        profitGoalProgressValue = 87.00;
        break;
      case TimeRange.ALL_TIME:
      default:
        // All time goal metrics
        actualChargebackValue = parseFloat(mockDashboardData.overview.goal.actualChargeback);
        chargebackGoalValue = parseFloat(mockDashboardData.overview.goal.chargebackGoal);
        profitGoalProgressValue = parseFloat(mockDashboardData.overview.goal.profitGoalProgress);
        break;
    }

    return {
      id: "1",
      label: "Profit Goal",
      actualChargeback: {
        value: actualChargebackValue,
        format: "percentage",
      },
      chargebackGoal: {
        value: chargebackGoalValue,
        format: "percentage",
      },
      profitGoalProgress: {
        value: profitGoalProgressValue,
        format: "percentage",
      },
    };
  }, [timeRange]);

  // Mock chargeback rate data
  const chargebackRateData = useMemo(() => {
    return getChartDataForTimeRange();
  }, [getChartDataForTimeRange]);

  // Set time range handler with loading simulation
  const setTimeRange = useCallback(
    async (newTimeRange: TimeRange, dateRange?: { from: Date; to: Date }) => {
      // Set loading states to simulate data fetching
      setMetricsLoading(true);
      setChartLoading(true);
      
      // Update the time range
      setTimeRangeState(newTimeRange);
      if (dateRange) {
        setCustomDateRange(dateRange);
      }

      // Simulate API delay for loading new data
      await new Promise((resolve) => setTimeout(resolve, 300));
      
      // Clear loading states
      setMetricsLoading(false);
      setChartLoading(false);
    },
    []
  );

  // Mock refetch function
  const refetch = useCallback(async () => {
    setLoading(true);
    setMetricsLoading(true);
    setChartLoading(true);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    setLastRefresh(new Date());
    setLoading(false);
    setMetricsLoading(false);
    setChartLoading(false);
  }, []);

  // Simulate initial loading
  useEffect(() => {
    if (!lastRefresh) {
      refetch();
    }
  }, [lastRefresh, refetch]);

  return {
    timeRange,
    customDateRange,
    metrics,
    goal,
    chargebackRateData,
    loading,
    metricsLoading,
    chartLoading,
    error,
    lastRefresh,
    setTimeRange,
    refetch,
  };
}
