// Performance monitoring utilities for development
export const performanceMonitor = {
  // Log component renders in development
  logRender: (componentName: string, props?: any) => {
    if (process.env.NODE_ENV === 'development') {
    }
  },

  // Log API calls in development
  logApiCall: (endpoint: string, cached: boolean = false) => {
    if (process.env.NODE_ENV === 'development') {
    }
  },

  // Measure component render time
  measureRender: (componentName: string, callback: () => void) => {
    if (process.env.NODE_ENV === 'development') {
      const start = performance.now();
      callback();
      const end = performance.now();
    } else {
      callback();
    }
  }
};
