import React from "react"

export const MetricCardSkeleton: React.FC = () => {
  return (
    <div className="bg-zinc-900 border border-zinc-800 rounded-2xl p-6 animate-pulse">
      <div className="flex items-start justify-between mb-4">
        <div className="h-5 w-24 bg-zinc-800 rounded"></div>
        <div className="w-6 h-6 bg-zinc-800 rounded"></div>
      </div>
      <div className="h-8 w-20 bg-zinc-800 rounded"></div>
    </div>
  )
}

export const GoalProgressCardSkeleton: React.FC = () => {
  return (
    <div className="bg-zinc-900 border border-zinc-800 rounded-2xl p-6 animate-pulse">
      <div className="h-6 w-32 bg-zinc-800 rounded mb-4"></div>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="h-4 w-20 bg-zinc-800 rounded"></div>
          <div className="h-4 w-16 bg-zinc-800 rounded"></div>
        </div>
        <div className="w-full h-2 bg-zinc-800 rounded-full"></div>
        <div className="flex justify-between items-center">
          <div className="h-4 w-32 bg-zinc-800 rounded"></div>
          <div className="h-4 w-24 bg-zinc-800 rounded"></div>
        </div>
      </div>
    </div>
  )
}
