import type React from "react"
import type { LucideProps } from "lucide-react"
import { EthocaLogo } from "../../../../components/ui/logos/ethoca-logo"
import { VerifiLogo } from "../../../../components/ui/logos/verifi-logo"

interface MetricCardProps {
  title: string
  value: string
  IconComponent: React.ElementType<LucideProps> // Allow any component that accepts LucideProps
  iconProps?: LucideProps
  valueClassName?: string
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, IconComponent, iconProps, valueClassName }) => {
  return (
    <div className="bg-transparent border border-zinc-800 rounded-2xl p-6 hover:border-zinc-600 transition-all hover:bg-zinc-900/50">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <span className="text-gray-500 text-base">{title}</span>
          {title === 'Alerts' && (
            <div className="flex items-center gap-2">
              <EthocaLogo width={16} height={16} />
              <VerifiLogo width={16} height={16} />
            </div>
          )}
        </div>
        <IconComponent {...iconProps} />
      </div>
      <div className={`text-4xl font-medium ${valueClassName || ""}`}>
        {title === 'Chargeback Rate (Projected)' ? <s>{value}</s> : value}
      </div>
    </div>
  )
}

export default MetricCard
