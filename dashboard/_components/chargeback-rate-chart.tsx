"use client"

import React from "react"
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"

interface ChargebackRateChartProps {
  data: Array<{
    date: string
    rate: number
    disputeCount?: number
    transactionCount?: number
  }>
  loading?: boolean
  timeRange?: string
}

const ChargebackRateChart: React.FC<ChargebackRateChartProps> = ({ data, loading, timeRange = 'all_time' }) => {
  const getTimeRangeTitle = () => {
    switch (timeRange) {
      case 'month':
        return 'This Month'
      case 'year':
        return 'This Year'
      case 'all_time':
        return 'All Time'
      case 'custom':
        return 'Custom Range'
      default:
        return 'All Time'
    }
  }

  // Debug logging for All Time data
  React.useEffect(() => {
    if (timeRange === 'all_time' && data && data.length > 0) {
      // console.log('🔍 All Time Chart Data:', {
      //   timeRange,
      //   dataLength: data.length,
      //   data: data.map(point => ({
      //     date: point.date,
      //     rate: point.rate,
      //     disputes: point.disputeCount,
      //     transactions: point.transactionCount
      //   }))
      // });
    }
  }, [data, timeRange]);

  // Calculate tick interval based on timeRange and data length
  const getXAxisTickInterval = () => {
    if (timeRange === 'all_time' && data.length > 6) {
      // For all time with many data points, show every other tick to prevent overcrowding
      return Math.ceil(data.length / 6)
    }
    if (timeRange === 'year' && data.length > 8) {
      // For yearly data, show every few ticks if too many
      return Math.ceil(data.length / 8)
    }
    // For monthly data or fewer points, show all ticks
    return 0
  }

  // Format X-axis labels based on timeRange
  const formatXAxisTick = (tickItem: string) => {
    if (timeRange === 'all_time') {
      // For all time, keep the existing format but ensure it's readable
      return tickItem
    }
    return tickItem
  }

  if (loading) {
    return (
      <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
        <div className="space-y-4">
          <div className="h-6 w-48 bg-zinc-800 rounded animate-pulse"></div>
          <div className="h-64 bg-zinc-800 rounded animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
        <h3 className="text-lg font-normal text-white mb-4">Chargeback Rate - {getTimeRangeTitle()}</h3>
        <div className="h-64 flex items-center justify-center text-gray-400">
          <p>No data available for {getTimeRangeTitle().toLowerCase()}</p>
        </div>
      </div>
    )
  }

  const renderTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const dataPoint = payload[0].payload;
      return (
        <div className="bg-[#16181C] border border-[#2F3336] rounded-lg p-3 shadow-lg">
          <p className="text-white text-sm font-medium">{`${label}`}</p>
          <p className="text-[#1D9BF0] text-sm">
            {`Rate: ${payload[0].value.toFixed(2)}%`}
          </p>
          {dataPoint.disputeCount !== undefined && (
            <p className="text-gray-400 text-xs">
              {`Disputes: ${dataPoint.disputeCount} / Transactions: ${dataPoint.transactionCount || 0}`}
            </p>
          )}
        </div>
      )
    }
    return null
  }

  return (
    <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
      <h3 className="text-lg font-normal text-white mb-6">Chargeback Rate - {getTimeRangeTitle()}</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: timeRange === 'all_time' ? 25 : 5, // More bottom margin for all time
            }}
          >
            <defs>
              <linearGradient id="colorRate" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#ffffff" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#ffffff" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="#2F3336" 
              horizontal={true}
              vertical={true}
            />
            <XAxis 
              dataKey="date" 
              stroke="#71767B"
              fontSize={timeRange === 'all_time' ? 10 : 12} // Smaller font for all time
              tickLine={false}
              axisLine={false}
              interval={getXAxisTickInterval()}
              tickFormatter={formatXAxisTick}
              angle={timeRange === 'all_time' ? -45 : 0} // Angle labels for all time
              textAnchor={timeRange === 'all_time' ? 'end' : 'middle'}
            />
            <YAxis 
              stroke="#71767B"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value.toFixed(0)}%`}
              dx={-10}
            />
            <Tooltip content={renderTooltip} />
            <Area
              type="monotone"
              dataKey="rate"
              stroke="#ffffff"
              strokeWidth={3}
              fillOpacity={1}
              fill="url(#colorRate)"
              connectNulls={false}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}

export default ChargebackRateChart
