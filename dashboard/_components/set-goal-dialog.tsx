"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "../../../../components/ui/dialog";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Label } from "../../../../components/ui/label";
import { Loader2, X } from "lucide-react";
import { toast } from "sonner";
import { mockSession } from "../../../../lib/mockData";

interface SetGoalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
  onGoalUpdated?: (goal: number) => void;
}

export function SetGoalDialog({
  open,
  onOpenChange,
  storeId,
  onGoalUpdated,
}: SetGoalDialogProps) {
  const session = mockSession;
  const [goal, setGoal] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [fetching, setFetching] = useState(false);

  const accessToken = session?.user?.access_token;
  const provider = session?.user?.provider;

  // Fetch current goal when dialog opens
  useEffect(() => {
    if (open && storeId) {
      fetchCurrentGoal();
    }
  }, [open, storeId]);

  const fetchCurrentGoal = async () => {
    try {
      setFetching(true);
      // Mock API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Mock current goal value
      const currentGoal = 1000; // Default mock goal
      setGoal(currentGoal ? currentGoal.toString() : "");
    } catch (error) {
      // Don't show error toast for fetch, just use empty value
    } finally {
      setFetching(false);
    }
  };

  const handleSave = async () => {
    if (!goal.trim()) {
      toast.error("Please enter a goal");
      return;
    }

    const goalValue = parseFloat(goal);
    if (isNaN(goalValue) || goalValue < 0) {
      toast.error("Please enter a valid positive number");
      return;
    }

    if (goalValue > 100) {
      toast.error("Goal should be 100% or less");
      return;
    }

    try {
      setLoading(true);
      // Mock API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success("Goal updated successfully");
      onGoalUpdated?.(goalValue);
      onOpenChange(false);
    } catch (error) {
      toast.error("Failed to save goal. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow only numbers and decimal point
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      setGoal(value);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md bg-[#16181C] border-[#2F3336] text-white">
        <div className="flex items-center justify-between">
          <DialogHeader className="flex-1">
            <DialogTitle className="text-xl font-normal text-white">
              Set Goal
            </DialogTitle>
          </DialogHeader>
        </div>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="goal" className="text-sm text-gray-400">
              Goal (%)
            </Label>
            <Input
              id="goal"
              type="text"
              value={goal}
              onChange={handleInputChange}
              placeholder="1.5"
              disabled={fetching || loading}
              className="bg-[#2F3336] border-[#2F3336] text-white placeholder:text-gray-500 focus:border-[#1D9BF0] h-12 text-base"
              autoFocus
            />
          </div>
        </div>

        <DialogFooter className="flex-row justify-end space-x-2 sm:space-x-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
            className="bg-transparent border-[#2F3336] text-white hover:bg-[#1A1A1A] hover:text-white cursor-pointer"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={loading || fetching || !goal.trim()}
            className="bg-[#1D9BF0] hover:bg-[#1A8CD8] text-white font-medium min-w-[100px] cursor-pointer"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Goal"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default SetGoalDialog;
