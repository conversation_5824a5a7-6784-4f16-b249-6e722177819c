"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "../../../../../components/ui/card"

const mockData = [
  { name: "Fraud", value: 28, color: "#000000" },
  { name: "Not Received", value: 18, color: "#404040" },
  { name: "Defective", value: 12, color: "#606060" },
  { name: "Duplicate", value: 8, color: "#808080" },
  { name: "Wrong Amount", value: 6, color: "#a0a0a0" },
  { name: "Other", value: 8, color: "#c0c0c0" },
]

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/90 border border-white/20 rounded-lg p-3 text-white">
        <p className="font-medium">{label}</p>
        <p className="text-sm text-gray-300">{payload[0].value} disputes</p>
      </div>
    )
  }
  return null
}

export default function DisputesByReasonChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalDisputes = chartData.reduce((sum, item) => sum + item.value, 0)
  const topReason = chartData[0]?.name || "N/A"

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Chargebacks by Reason</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis type="number" stroke="#9ca3af" fontSize={12} tickLine={false} axisLine={false} />
              <YAxis
                type="category"
                dataKey="name"
                stroke="#9ca3af"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                width={80}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill="#000000" radius={[0, 4, 4, 0]} maxBarSize={40} />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Summary stats */}
      </CardContent>
    </Card>
  )
}
