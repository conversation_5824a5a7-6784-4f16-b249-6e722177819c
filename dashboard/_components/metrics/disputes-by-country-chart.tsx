"use client"

import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "../../../../../components/ui/card"

const mockData = [
  { name: "United States", value: 45, color: "#000000" },
  { name: "Canada", value: 25, color: "#202020" },
  { name: "United Kingdom", value: 20, color: "#404040" },
  { name: "Germany", value: 15, color: "#606060" },
  { name: "France", value: 10, color: "#808080" },
  { name: "Australia", value: 8, color: "#a0a0a0" },
  { name: "Netherlands", value: 6, color: "#c0c0c0" },
  { name: "Italy", value: 5, color: "#e0e0e0" },
]

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/95 border border-white/20 rounded-lg p-3 text-white shadow-lg backdrop-blur-sm">
        <p className="font-medium text-white">{label}</p>
        <p className="text-sm text-gray-300">{payload[0].value} disputes</p>
      </div>
    )
  }
  return null
}

export default function DisputesByCountryChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalDisputes = chartData.reduce((sum, item) => sum + item.value, 0)
  const topCountry = chartData[0]?.name || "N/A"

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Chargebacks by Country</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis type="number" stroke="#9ca3af" fontSize={12} tickLine={false} axisLine={false} />
              <YAxis
                type="category"
                dataKey="name"
                stroke="#9ca3af"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                width={100}
                tick={{ fontSize: 10 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill="#000000" radius={[0, 4, 4, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Summary stats */}
      </CardContent>
    </Card>
  )
}
