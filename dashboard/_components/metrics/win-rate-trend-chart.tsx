"use client"

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card'

const mockData = [
  { month: 'Jan', winRate: 78, disputes: 12 },
  { month: 'Feb', winRate: 82, disputes: 8 },
  { month: 'Mar', winRate: 75, disputes: 15 },
  { month: 'Apr', winRate: 89, disputes: 6 },
  { month: 'May', winRate: 85, disputes: 9 },
  { month: 'Jun', winRate: 92, disputes: 4 },
  { month: 'Jul', winRate: 88, disputes: 7 },
  { month: 'Aug', winRate: 91, disputes: 5 },
  { month: 'Sep', winRate: 87, disputes: 8 },
  { month: 'Oct', winRate: 94, disputes: 3 },
  { month: 'Nov', winRate: 90, disputes: 6 },
  { month: 'Dec', winRate: 93, disputes: 4 },
]

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/90 border border-white/20 rounded-lg p-3 text-white">
        <p className="font-medium">{label}</p>
        <p className="text-sm text-green-400">
          Win Rate: {payload[0].value}%
        </p>
        <p className="text-sm text-white/70">
          Disputes: {payload[1]?.value || 0}
        </p>
      </div>
    )
  }
  return null
}

export default function WinRateTrendChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalDisputes = chartData.reduce((sum, item) => sum + item.disputes, 0)
  const avgWinRate = totalDisputes > 0 ? 
    Math.round(chartData.reduce((sum, item) => sum + (item.winRate * item.disputes), 0) / totalDisputes) : 0
  const bestMonth = Math.max(...chartData.map(item => item.winRate))
  const lowestMonth = Math.min(...chartData.map(item => item.winRate))

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Win Rate Trend</CardTitle>
        <p className="text-sm text-white/50">Chargeback win rate over time</p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="winRateGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis 
                dataKey="month" 
                stroke="#9ca3af" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis 
                stroke="#9ca3af" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="winRate"
                stroke="#10b981"
                strokeWidth={2}
                fill="url(#winRateGradient)"
                dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary stats */}
        <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-white/10">
          <div className="text-center">
            <div className="text-2xl font-light text-green-400">{avgWinRate}%</div>
            <div className="text-xs text-white/50">Avg Win Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-blue-400">{bestMonth}%</div>
            <div className="text-xs text-white/50">Best Month</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-yellow-400">{lowestMonth}%</div>
            <div className="text-xs text-white/50">Lowest Month</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
