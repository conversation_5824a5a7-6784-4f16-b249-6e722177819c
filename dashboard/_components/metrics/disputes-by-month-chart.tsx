"use client"

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card'

// Mock data based on real ShopifyDispute.createdAt field
const mockData = [
  { month: 'Jan', disputes: 8, amount: 2400 },
  { month: 'Feb', disputes: 12, amount: 3600 },
  { month: 'Mar', disputes: 15, amount: 4500 },
  { month: 'Apr', disputes: 6, amount: 1800 },
  { month: 'May', disputes: 9, amount: 2700 },
  { month: 'Jun', disputes: 4, amount: 1200 },
  { month: 'Jul', disputes: 7, amount: 2100 },
  { month: 'Aug', disputes: 5, amount: 1500 },
  { month: 'Sep', disputes: 8, amount: 2400 },
  { month: 'Oct', disputes: 3, amount: 900 },
  { month: 'Nov', disputes: 6, amount: 1800 },
  { month: 'Dec', disputes: 4, amount: 1200 },
]

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/95 border border-white/20 rounded-lg p-3 text-white shadow-lg backdrop-blur-sm">
        <p className="font-medium text-white">{label}</p>
        <p className="text-sm text-red-400">
          {payload[0].value} disputes
        </p>
        <p className="text-sm text-white/70">
          ${payload[1]?.value || 0} total amount
        </p>
      </div>
    )
  }
  return null
}

export default function DisputesByMonthChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalDisputes = chartData.reduce((sum, item) => sum + item.disputes, 0)
  const peakMonth = Math.max(...chartData.map(item => item.disputes))
  const lowestMonth = Math.min(...chartData.map(item => item.disputes))

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Disputes by Month</CardTitle>
        <p className="text-sm text-white/50">Chargeback disputes over time</p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="disputesGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#ef4444" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#ef4444" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis 
                dataKey="month" 
                stroke="#9ca3af" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis 
                stroke="#9ca3af" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="disputes"
                stroke="#ef4444"
                strokeWidth={2}
                fill="url(#disputesGradient)"
                dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2 }}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary stats */}
        <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-white/10">
          <div className="text-center">
            <div className="text-2xl font-light text-red-400">{totalDisputes}</div>
            <div className="text-xs text-white/50">Total Disputes</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-orange-400">{peakMonth}</div>
            <div className="text-xs text-white/50">Peak Month</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-green-400">{lowestMonth}</div>
            <div className="text-xs text-white/50">Lowest Month</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
