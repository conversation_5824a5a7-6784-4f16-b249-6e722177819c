"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from 'recharts'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card'

// Mock data based on real Block.feedbackStatus field (PENDING, SENT, FAILED)
const mockData = [
  { name: 'Blocked', value: 45, color: '#10b981' },
  { name: 'Pending', value: 25, color: '#f59e0b' },
  { name: 'Failed', value: 15, color: '#ef4444' },
  { name: 'No Action', value: 15, color: '#6b7280' },
]

const CustomTooltip = ({ active, payload, data }: any) => {
  if (active && payload && payload.length) {
    const total = data.reduce((sum: number, item: any) => sum + item.value, 0)
    return (
      <div className="bg-black/95 border border-white/20 rounded-lg p-3 text-white shadow-lg backdrop-blur-sm">
        <p className="font-medium text-white">{payload[0].name}</p>
        <p className="text-sm text-white/70">
          {payload[0].value} alerts ({((payload[0].value / total) * 100).toFixed(1)}%)
        </p>
      </div>
    )
  }
  return null
}

export default function AlertsByOutcomeChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalAlerts = chartData.reduce((sum, item) => sum + item.value, 0)
  const successRate = chartData.find(item => item.name === 'Blocked')?.value || 0
  const pendingRate = chartData.find(item => item.name === 'Pending')?.value || 0

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Alerts by Outcome</CardTitle>
        <p className="text-sm text-white/50">Distribution of alert outcomes</p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={50}
                outerRadius={90}
                paddingAngle={5}
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip data={chartData} />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value) => (
                  <span className="text-white/80 text-sm">{value}</span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary stats */}
        <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-white/10">
          <div className="text-center">
            <div className="text-2xl font-light text-green-400">{totalAlerts > 0 ? Math.round((successRate / totalAlerts) * 100) : 0}%</div>
            <div className="text-xs text-white/50">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-orange-400">{totalAlerts > 0 ? Math.round((pendingRate / totalAlerts) * 100) : 0}%</div>
            <div className="text-xs text-white/50">Pending</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
