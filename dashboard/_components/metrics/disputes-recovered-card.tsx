"use client"

import { Card, CardContent } from "../../../../../components/ui/card"

// Mock data
const mockData = {
  count: 56,
}

export default function DisputesRecoveredCard() {
  const { count } = mockData
  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardContent className="p-6">
        <div className="text-center">
          <div className="text-white/70 text-sm mb-2">Disputes Recovered</div>
          <div className="text-3xl font-light text-white">{count.toLocaleString()}</div>
        </div>
      </CardContent>
    </Card>
  )
}
