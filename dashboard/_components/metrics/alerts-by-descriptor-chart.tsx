"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card'

// Mock data based on real Block.descriptor field
const mockData = [
  { name: 'AMAZON.COM', value: 23, color: '#3b82f6' },
  { name: 'NETFLIX.COM', value: 18, color: '#10b981' },
  { name: 'SPOTIFY.COM', value: 12, color: '#f59e0b' },
  { name: 'UBER.COM', value: 10, color: '#ef4444' },
  { name: 'DOORDASH.COM', value: 8, color: '#8b5cf6' },
  { name: 'OTHER', value: 9, color: '#6b7280' },
]

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/95 border border-white/20 rounded-lg p-3 text-white shadow-lg backdrop-blur-sm">
        <p className="font-medium text-white">{label}</p>
        <p className="text-sm text-blue-400">
          {payload[0].value} alerts
        </p>
      </div>
    )
  }
  return null
}

export default function AlertsByDescriptorChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalAlerts = chartData.reduce((sum, item) => sum + item.value, 0)
  const topDescriptor = chartData[0]?.name || 'N/A'

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Alerts by Descriptor</CardTitle>
        <p className="text-sm text-white/50">Most common merchant descriptors</p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis 
                type="number"
                stroke="#9ca3af" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis 
                type="category"
                dataKey="name"
                stroke="#9ca3af" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
                width={100}
                tick={{ fontSize: 10 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="value" 
                fill="#3b82f6" 
                radius={[0, 4, 4, 0]}
                maxBarSize={40}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary stats */}
        <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-white/10">
          <div className="text-center">
            <div className="text-2xl font-light text-blue-400">{totalAlerts}</div>
            <div className="text-xs text-white/50">Total Alerts</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-green-400">{topDescriptor}</div>
            <div className="text-xs text-white/50">Top Descriptor</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
