"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON> } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "../../../../../components/ui/card"

const mockData = [
  { name: "ETHOCA", value: 65, color: "#10b981" },
  { name: "RDR", value: 35, color: "#3b82f6" },
]

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/90 border border-white/20 rounded-lg p-3 text-white">
        <p className="font-medium">{payload[0].payload.name}</p>
        <p className="text-sm text-white/70">{payload[0].value} alerts</p>
      </div>
    )
  }
  return null
}

export default function AlertsBySourceChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalAlerts = chartData.reduce((sum, item) => sum + item.value, 0)
  const ethocaAlerts = chartData.find((item) => item.name === "ETHOCA")?.value || 0
  const rdrAlerts = chartData.find((item) => item.name === "RDR")?.value || 0

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Alerts by Source</CardTitle>
        <p className="text-sm text-white/50">Distribution of alerts by source</p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <XAxis dataKey="name" axisLine={false} tickLine={false} tick={{ fill: "#ffffff80", fontSize: 12 }} />
              <YAxis axisLine={false} tickLine={false} tick={{ fill: "#ffffff80", fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill={(entry: any) => entry.color} radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Summary stats */}
        <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-white/10">
          <div className="text-center">
            <div className="text-2xl font-light text-green-400">{ethocaAlerts}</div>
            <div className="text-xs text-white/50">ETHOCA Alerts</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-blue-400">{rdrAlerts}</div>
            <div className="text-xs text-white/50">RDR Alerts</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
