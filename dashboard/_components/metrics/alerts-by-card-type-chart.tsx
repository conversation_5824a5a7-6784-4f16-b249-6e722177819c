"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON>r, <PERSON>, Tooltip } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card'

const mockData = [
  { name: 'Visa', value: 45, color: '#1e40af' },
  { name: 'Mastercard', value: 38, color: '#dc2626' },
  { name: 'American Express', value: 12, color: '#059669' },
  { name: 'Discover', value: 5, color: '#ea580c' },
]

const CustomTooltip = ({ active, payload, data }: any) => {
  if (active && payload && payload.length) {
    const total = data.reduce((sum: number, item: any) => sum + item.value, 0)
    return (
      <div className="bg-black/90 border border-white/20 rounded-lg p-3 text-white">
        <p className="font-medium">{payload[0].name}</p>
        <p className="text-sm text-white/70">
          {payload[0].value} alerts ({((payload[0].value / total) * 100).toFixed(1)}%)
        </p>
      </div>
    )
  }
  return null
}

export default function AlertsByCardTypeChart() {
  // Use mock data
  const chartData = mockData

  // Calculate summary stats
  const totalAlerts = chartData.reduce((sum, item) => sum + item.value, 0)
  const topCardType = chartData[0]?.name || 'N/A'

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-light text-white">Alerts by Card Type</CardTitle>
        <p className="text-sm text-white/50">Distribution of alerts by card network</p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip data={chartData} />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value, entry: any) => (
                  <span className="text-white/80 text-sm">{value}</span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary stats */}
        <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-white/10">
          <div className="text-center">
            <div className="text-2xl font-light text-blue-400">{totalAlerts}</div>
            <div className="text-xs text-white/50">Total Alerts</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-light text-green-400">{topCardType}</div>
            <div className="text-xs text-white/50">Top Card Type</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
