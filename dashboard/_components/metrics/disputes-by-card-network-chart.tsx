"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from 'recharts'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card'
import { Info, BarChart3, Table, DollarSign, Hash } from 'lucide-react'

// Mock data based on the image
const mockData = [
  { name: 'Visa', value: 60300, percentage: 37, color: '#1e40af' },
  { name: 'PayPal', value: 53700, percentage: 33, color: '#3b82f6' },
  { name: 'Mastercard', value: 26100, percentage: 16, color: '#60a5fa' },
  { name: 'Amex', value: 22500, percentage: 14, color: '#1e3a8a' },
]

const totalValue = mockData.reduce((sum, item) => sum + item.value, 0)

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/95 border border-white/20 rounded-lg p-3 text-white shadow-lg backdrop-blur-sm">
        <p className="font-medium text-white">{payload[0].name}</p>
        <p className="text-sm text-blue-400">
          ${payload[0].value.toLocaleString()} ({payload[0].payload.percentage}%)
        </p>
      </div>
    )
  }
  return null
}

export default function DisputesByCardNetworkChart() {
  // Fixed view modes (no state in mock version)
  const viewMode = 'value'
  const displayMode = 'chart'

  // Use mock data
  const chartData = mockData

  return (
    <Card className="bg-white/[0.02] backdrop-blur-sm border border-white/10">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg font-light text-white">Disputes by Card Network</CardTitle>
            <Info className="w-4 h-4 text-white/50" />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-white/50">Last 6 Months</span>
            <div className="flex items-center bg-white/5 rounded-lg p-1">
              <button
                className="px-3 py-1 rounded text-xs font-medium transition-colors bg-white text-black"
              >
                <DollarSign className="w-3 h-3 inline mr-1" />
                By Value
              </button>
              <button
                className="px-3 py-1 rounded text-xs font-medium transition-colors text-white/70 hover:text-white"
              >
                <Hash className="w-3 h-3 inline mr-1" />
                By Count
              </button>
            </div>
            <div className="flex items-center bg-white/5 rounded-lg p-1">
              <button
                className="p-1 rounded transition-colors bg-white text-black"
              >
                <BarChart3 className="w-4 h-4" />
              </button>
              <button
                className="p-1 rounded transition-colors text-white/70 hover:text-white"
              >
                <Table className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {displayMode === 'chart' ? (
          <div className="flex gap-6">
            {/* List View */}
            <div className="flex-1 space-y-4">
              {chartData.map((item) => (
                <div key={item.name} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/80 text-sm">{item.name}</span>
                    <span className="text-white font-medium">
                      ${item.value.toLocaleString()} ({item.percentage || Math.round((item.value / totalValue) * 100)}%)
                    </span>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div 
                      className="h-2 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${item.percentage || Math.round((item.value / totalValue) * 100)}%`, 
                        backgroundColor: item.color 
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
            
            {/* Donut Chart */}
            <div className="flex-1 h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend 
                    verticalAlign="bottom" 
                    height={36}
                    formatter={(value) => (
                      <span className="text-white/80 text-sm">{value}</span>
                    )}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        ) : (
          /* Table View */
          <div className="space-y-4">
            <div className="grid grid-cols-4 gap-4 text-sm font-medium text-white/70 border-b border-white/10 pb-2">
              <div>Card Network</div>
              <div className="text-right">Count</div>
              <div className="text-right">Value</div>
              <div className="text-right">Percentage</div>
            </div>
            {chartData.map((item) => (
              <div key={item.name} className="grid grid-cols-4 gap-4 text-sm">
                <div className="text-white">{item.name}</div>
                <div className="text-right text-white">
                  {Math.round(item.value / 1000)}k
                </div>
                <div className="text-right text-white">
                  ${item.value.toLocaleString()}
                </div>
                <div className="text-right text-white">{item.percentage || Math.round((item.value / totalValue) * 100)}%</div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
