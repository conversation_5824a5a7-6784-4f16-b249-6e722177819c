"use client"

import React from "react"
import type { LucideIcon } from "lucide-react"

interface MetricCardProps {
  title: string
  value: string | number
  IconComponent: LucideIcon
  iconProps?: any
  valueClassName?: string
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  IconComponent, 
  iconProps = {}, 
  valueClassName = "" 
}) => {
  return (
    <div className="bg-transparent border border-zinc-800 rounded-xl p-5 flex items-center gap-4 transition-all duration-200 hover:border-zinc-700">
      <div className="flex-shrink-0">
        <IconComponent {...iconProps} />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm text-gray-400 mb-1">{title}</p>
        <p className={`text-2xl font-semibold truncate ${valueClassName}`}>
          {value}
        </p>
      </div>
    </div>
  )
}

export default MetricCard
