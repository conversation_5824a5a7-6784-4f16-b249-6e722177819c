"use client";

import DashboardContent from "./content";
import {
  mockSelectedStore,
  mockAlertInfo,
  mockLoadingStates,
} from "../../../lib/mockData";

export default function DashboardPage() {
  const store = mockSelectedStore;
  const alertInfosLoading = mockLoadingStates.alerts;
  const alertInfos = mockAlertInfo;

  return (
    <DashboardContent
      alertInfos={alertInfos}
      alertInfosLoading={alertInfosLoading}
      storeId={store?.id}
    />
  );
}
