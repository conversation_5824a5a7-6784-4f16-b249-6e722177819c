import type React from "react"
import {
  Bar<PERSON>hart3,
  Package,
  Shirt,
  Palette,
  TrendingUp,
  DollarSign,
  Users,
  Settings,
  FileSearch,
  type LucideIcon,
} from "lucide-react"

export type TimeFilterOption = "This Month" | "This Year" | "All Time"

export const TIME_FILTERS: readonly TimeFilterOption[] = ["This Month", "This Year", "All Time"]

export interface SidebarNavItem {
  label: string
  icon: LucideIcon
  view?: string // View to switch to, e.g., 'dashboard'
  count?: number
  hasSubmenu?: boolean
}

export interface Store {
  id: string
  name: string
  logo?: React.ElementType // Optional: if stores have specific logos
}

export const MOCK_STORES: Store[] = [
  { id: "store1", name: "Quantum Threads Co." },
  { id: "store2", name: "Nebula Novelties Inc." },
  { id: "store3", name: "Galaxy Gadgets Ltd." },
  { id: "store4", name: "Cosmic Comforts" },
]

export const SIDEBAR_NAV_ITEMS: SidebarNavItem[] = [
  { label: "Profit Dashboard", icon: BarChart3, view: "dashboard" },
  { label: "Orders", icon: Package, count: 5, hasSubmenu: true },
  { label: "My Products", icon: Shirt },
  { label: "Branding", icon: Palette },
  { label: "Catalogs", icon: Package, hasSubmenu: true }, // Using Package icon as placeholder
  { label: "Find Products", icon: FileSearch, hasSubmenu: true },
  { label: "Analytics", icon: TrendingUp, hasSubmenu: true },
  { label: "Finance", icon: DollarSign, hasSubmenu: true },
  { label: "Affiliate", icon: Users },
  { label: "Settings", icon: Settings },
]

export interface DashboardMetric {
  id: string
  label: string
  value: number
  format?: "number" | "percentage"
}

export interface DashboardGoal {
  id: string
  label: string
  actualChargeback: {
    value: number,
    format: "number" | "percentage"
  };
  chargebackGoal: {
    value: number,
    format: "number" | "percentage"
  };
  profitGoalProgress: {
    value: number,
    format: "number" | "percentage"
  };
}

export enum TimeRange {
  THIS_MONTH = "month",
  THIS_YEAR = "year",
  ALL_TIME = "all_time",
  CUSTOM = "custom",
}

export function getDateRangeFromFilter(filter: TimeFilterOption): { from: Date; to: Date } {
  const now = new Date()

  switch (filter) {
    case "This Month":
      return {
        from: new Date(now.getFullYear(), now.getMonth(), 1),
        to: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
      }

    case "This Year":
      return {
        from: new Date(now.getFullYear(), 0, 1),
        to: new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999)
      }

    case "All Time":
      // Return a very early date to represent "all time"
      return {
        from: new Date(2000, 0, 1),
        to: now
      }

    default:
      return { 
        from: new Date(now.getFullYear(), now.getMonth(), 1),
        to: now 
      }
  }
}
