"use client"

const mockData = [
  { name: "Fraud", count: 1247 },
  { name: "Product Not Received", count: 892 },
  { name: "Defective Product", count: 634 },
  { name: "Duplicate Charge", count: 421 },
  { name: "Wrong Amount", count: 318 },
]

export default function TopChargebackReasons() {
  const formatCount = (count: number) => {
    return new Intl.NumberFormat("en-US").format(count)
  }

  return (
    <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
      <h3 className="text-xl font-normal text-white mb-6">Top Chargeback Reasons</h3>

      <div className="space-y-4">
        {mockData.map((item, index) => (
          <div
            key={item.name}
            className="flex items-center justify-between py-3 border-b border-zinc-800 last:border-b-0"
          >
            <div className="flex-1">
              <h4 className="text-white font-medium text-base mb-1">{item.name}</h4>
            </div>

            <div className="flex items-center space-x-3">
              <span className="text-white font-semibold text-lg">{formatCount(item.count)}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-zinc-800">
        <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
          View All Reasons
        </button>
      </div>
    </div>
  )
}
