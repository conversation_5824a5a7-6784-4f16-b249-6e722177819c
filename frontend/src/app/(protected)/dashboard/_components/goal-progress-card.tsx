import type React from "react"
import { memo } from "react"

interface GoalProgressCardProps {
  title: string
  currentValue: number
  goalValue: number
  currency?: string
  progressColor: string
}

const GoalProgressCard: React.FC<GoalProgressCardProps> = memo(({
  title,
  currentValue,
  goalValue,
  currency = "",
  progressColor,
}) => {
  // Handle "lower is better" case for chargeback rate goal
  const isLowerBetter = title.toLowerCase().includes("rate goal")


  const formattedCurrentValue = `${isLowerBetter ? currentValue.toLocaleString() : currentValue.toLocaleString()}${currency}`
  const formattedGoalValue = `${isLowerBetter ? goalValue.toLocaleString() : goalValue.toLocaleString()}${currency}`

  // Calculate percentage based on current progress toward goal, capped at 100%
  const percentage = Math.min((goalValue / currentValue) * 100, 100)

  return (
    <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-400">{title}</span>
          <span className="text-white">
            {`${formattedCurrentValue} / ${formattedGoalValue}`}
          </span>
        </div>
        <div className="w-full bg-zinc-800 rounded-full h-3">
          <div
            className={`${progressColor} h-3 rounded-full transition-all duration-500`}
            style={{ width: `${percentage}%` }}
            role="progressbar"
            aria-valuenow={percentage}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`${title} progress`}
          ></div>
        </div>
        <span className="text-sm text-gray-500">{percentage.toFixed(2)}% Complete</span>
      </div>
    </div>
  )
})

GoalProgressCard.displayName = 'GoalProgressCard'

export default GoalProgressCard
