"use client"

const mockData = [
  { name: "United States", code: "US", count: 2847 },
  { name: "Canada", code: "CA", count: 1523 },
  { name: "United Kingdom", code: "GB", count: 1204 },
  { name: "Germany", code: "DE", count: 967 },
  { name: "France", code: "FR", count: 743 },
]

export default function TopChargebackCountries() {
  const formatCount = (count: number) => {
    return new Intl.NumberFormat("en-US").format(count)
  }

  const getCountryFlag = (countryCode: string) => {
    const codePoints = countryCode
      .toUpperCase()
      .split("")
      .map((char) => 127397 + char.charCodeAt(0))
    return String.fromCodePoint(...codePoints)
  }

  return (
    <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
      <h3 className="text-xl font-normal text-white mb-6">Top Chargeback Countries</h3>

      <div className="space-y-4">
        {mockData.map((item, index) => (
          <div
            key={item.name}
            className="flex items-center justify-between py-3 border-b border-zinc-800 last:border-b-0"
          >
            <div className="flex items-center flex-1">
              <span className="text-2xl mr-3">{getCountryFlag(item.code)}</span>
              <h4 className="text-white font-medium text-base">{item.name}</h4>
            </div>

            <div className="flex items-center space-x-3">
              <span className="text-white font-semibold text-lg">{formatCount(item.count)}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-zinc-800">
        <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
          View All Countries
        </button>
      </div>
    </div>
  )
}
