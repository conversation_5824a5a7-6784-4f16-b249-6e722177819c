"use client";

import type React from "react";
import { memo, useMemo } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, AlertTriangle } from "lucide-react";
import DashboardHeader from "./header";
import MetricCard from "./_components/metric-card-optimized";
import GoalProgressCard from "./_components/goal-progress-card";
import ChargebackRateChart from "./_components/chargeback-rate-chart";
import { useDashboard } from "./_hooks";
import {
  MetricCardSkeleton,
  GoalProgressCardSkeleton,
} from "./_components/skeleton";
import TopChargebackReasons from "./_components/top-chargeback-reasons";
import TopChargebackCountries from "./_components/top-chargeback-countries";

const DashboardContent: React.FC = () => {
  const {
    timeRange,
    customDateRange,
    metrics,
    goal,
    chargebackRateData,
    metricsLoading,
    chartLoading,
    error,
    setTimeRange,
    refetch,
  } = useDashboard({});

  // Memoize metrics processing
  const CHARGEBACK_METRICS = useMemo(() => {
    const getMetricValue = (label: string) => {
      const metric = metrics?.find((m) => m.label === label);
      if (!metric) return "0";

      if (metric.format === "percentage") {
        return typeof metric.value === "number"
          ? `${metric.value.toFixed(2)}%`
          : `${metric.value}%`;
      }
      return metric.value.toString();
    };

    return [
      {
        title: "Alerts",
        value: getMetricValue("Alerts"),
        IconComponent: BellRing,
        iconProps: { className: "w-6 h-6 text-gray-400" },
      },
      {
        title: "Blocked",
        value: getMetricValue("Blocked"),
        IconComponent: ShieldCheck,
        iconProps: { className: "w-6 h-6 text-gray-400" },
        valueClassName: "",
      },
      {
        title: "Chargeback Rate",
        value: getMetricValue("Chargeback Rate"),
        IconComponent: Percent,
        iconProps: { className: "text-2xl text-gray-400" },
        valueClassName: "text-green-500",
      },
      {
        title: "Chargeback Rate (Projected)",
        value: getMetricValue("Chargeback Rate (Projected)"),
        IconComponent: AlertTriangle,
        iconProps: { className: "w-6 h-6 text-gray-400" },
        valueClassName: "",
      },
    ];
  }, [metrics]);

  return (
    <div className="relative w-full bg-black text-white p-4">
      <div className="relative z-10 mx-auto">
        <DashboardHeader
          timeRange={timeRange}
          onTimeRangeChange={setTimeRange}
          customDateRange={customDateRange}
        />

        {/* Error state */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
            <p className="text-red-400">Error loading dashboard: {error}</p>
            <button
              onClick={refetch}
              className="mt-2 text-sm text-red-300 hover:text-red-200 underline"
            >
              Try again
            </button>
          </div>
        )}

        {/* Chargeback Rate Chart Section - Independent Loading */}
        <div className="my-8 sm:my-12">
          <ChargebackRateChart
            data={chargebackRateData}
            loading={chartLoading}
            timeRange={timeRange}
          />
        </div>

        {/* Metrics Section - Progressive Loading */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
          {metricsLoading
            ? // Show skeleton only for metrics loading
              [1, 2, 3, 4].map((i) => <MetricCardSkeleton key={i} />)
            : // Show metrics immediately when available
              CHARGEBACK_METRICS.map((metric) => (
                <MetricCard key={metric.title} {...metric} />
              ))}
        </div>

        {/* Goal Progress Section - Progressive Loading */}
        {metricsLoading ? (
          <div className="mt-8 sm:mt-12">
            <div className="h-8 w-48 bg-zinc-800 rounded-full mb-6 animate-pulse"></div>
            <GoalProgressCardSkeleton />
          </div>
        ) : (
          goal && (
            <div className="mt-8 sm:mt-12">
              <div className="flex items-center gap-3 mb-6">
                <h2 className="text-2xl font-normal">Goal</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                <GoalProgressCard
                  title="Chargeback Rate"
                  currentValue={goal.actualChargeback.value}
                  goalValue={goal.chargebackGoal.value}
                  currency="%"
                  progressColor="bg-white"
                />
              </div>
            </div>
          )
        )}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8 sm:mt-12">
          <TopChargebackReasons />
          <TopChargebackCountries />
        </div>
      </div>
    </div>
  );
};

export default memo(DashboardContent);
