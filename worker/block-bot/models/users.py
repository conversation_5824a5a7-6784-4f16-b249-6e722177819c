from sqlalchemy import Column, String, Boolean, DateTime, Text, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID, TIMESTAMP
from datetime import datetime
from .base import Base

class User(Base):
    __tablename__ = 'users'

    id = Column(String, primary_key=True)
    code = Column(String, unique=True, nullable=False)
    full_name = Column('full_name', String, nullable=False)
    phone_number = Column('phone_number', String, nullable=True)
    email = Column(String, unique=True, nullable=False)
    password = Column(String, nullable=False)
    address = Column(String, nullable=True)
    gender = Column(String, nullable=False)
    birth_date = Column('birth_date', TIMESTAMP(timezone=True), nullable=False)
    status = Column(Boolean, default=True, nullable=False)
    note = Column(Text, nullable=True)
    is_active = Column('is_active', Boolean, default=True, nullable=False)
    uninstalled_at = Column('uninstalled_at', TIMESTAMP(timezone=True), nullable=True)
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    linked_stores = relationship("LinkedStore", back_populates="user")
    matched_blocks = relationship("MatchedBlock", back_populates="user")
    unified_transactions = relationship("UnifiedTransaction", back_populates="user")
    unified_disputes = relationship("UnifiedDispute", back_populates="user")

    # PostgreSQL specific indexes
    __table_args__ = (
        Index('ix_users_is_active', 'is_active'),
    )

    def __repr__(self):
        return f"<User(id='{self.id}', email='{self.email}', full_name='{self.full_name}')>"