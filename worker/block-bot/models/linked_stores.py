from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, Index, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSO<PERSON>, UUID, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class LinkedStore(Base):
    __tablename__ = 'linked_stores'

    id = Column(String, primary_key=True)
    user_id = Column('user_id', String, ForeignKey('users.id'), nullable=False)
    store_name = Column('store_name', String, nullable=False)
    provider = Column(String, nullable=False)
    provider_store_id = Column('provider_store_id', String, nullable=True)
    data = Column(JSON, nullable=False)
    is_active = Column('is_active', Boolean, default=True, nullable=False)
    uninstalled_at = Column('uninstalled_at', TIMESTAMP(timezone=True), nullable=True)
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    user = relationship("User", back_populates="linked_stores")
    alert_infos = relationship("AlertInfo", back_populates="store")
    matched_blocks = relationship("MatchedBlock", back_populates="linked_store")
    unified_transactions = relationship("UnifiedTransaction", back_populates="linked_store")
    unified_disputes = relationship("UnifiedDispute", back_populates="linked_store")
    shopify_transactions = relationship("ShopifyTransaction", back_populates="linked_store")
    shopify_disputes = relationship("ShopifyDispute", back_populates="linked_store")
    stripe_payment_intents = relationship("StripePaymentIntent", back_populates="linked_store")
    stripe_charges = relationship("StripeCharge", back_populates="linked_store")
    stripe_disputes = relationship("StripeDispute", back_populates="linked_store")

    # PostgreSQL specific constraints and indexes
    __table_args__ = (
        UniqueConstraint('provider', 'provider_store_id', name='uq_linked_stores_provider_store'),
        Index('ix_linked_stores_is_active', 'is_active'),
        Index('ix_linked_stores_provider_store_active', 'provider_store_id', 'is_active'),
    )

    def __repr__(self):
        return f"<LinkedStore(id='{self.id}', store_name='{self.store_name}', provider='{self.provider}')>"