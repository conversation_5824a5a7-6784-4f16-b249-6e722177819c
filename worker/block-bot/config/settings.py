"""
Production settings for Block Matcher
"""
import os
from typing import Union

def get_env_bool(key: str, default: bool = False) -> bool:
    """Get boolean from environment variable"""
    value = os.getenv(key, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')

def get_env_int(key: str, default: int) -> int:
    """Get integer from environment variable"""
    try:
        return int(os.getenv(key, str(default)))
    except (ValueError, TypeError):
        return default

def get_env_float(key: str, default: float) -> float:
    """Get float from environment variable"""
    try:
        return float(os.getenv(key, str(default)))
    except (ValueError, TypeError):
        return default

class Settings:
    """Production settings for Block Matcher with environment variable support"""
    
    # Performance Settings
    BATCH_SIZE = get_env_int('BATCH_SIZE', 500)
    MAX_UPSERT_BATCH_SIZE = get_env_int('MAX_UPSERT_BATCH_SIZE', 1000)
    MAX_RETRIES = get_env_int('MAX_RETRIES', 3)
    SLOW_OPERATION_THRESHOLD_SECONDS = get_env_float('SLOW_OPERATION_THRESHOLD_SECONDS', 30.0)
    
    # Memory Management
    MEMORY_CLEANUP_ENABLED = get_env_bool('MEMORY_CLEANUP_ENABLED', True)
    
    # Monitoring and Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    PROGRESS_LOG_ENABLED = get_env_bool('PROGRESS_LOG_ENABLED', True)
    PERFORMANCE_MONITORING_ENABLED = get_env_bool('PERFORMANCE_MONITORING_ENABLED', True)
    
    # Scheduling Settings
    SCHEDULE_INTERVAL_MINUTES = get_env_int('SCHEDULE_INTERVAL_MINUTES', 15)
    
    # Safety Limits
    MAX_BATCH_SIZE_LIMIT = 1000  # Hard limit to prevent accidental large batches
    
    @classmethod
    def get_safe_batch_size(cls) -> int:
        """Get batch size with safety enforcement"""
        return min(cls.BATCH_SIZE, cls.MAX_BATCH_SIZE_LIMIT)
    
    @classmethod
    def log_settings(cls):
        """Log current settings for debugging"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Block Matcher Settings:")
        logger.info(f"  BATCH_SIZE: {cls.BATCH_SIZE}")
        logger.info(f"  MAX_UPSERT_BATCH_SIZE: {cls.MAX_UPSERT_BATCH_SIZE}")
        logger.info(f"  MAX_RETRIES: {cls.MAX_RETRIES}")
        logger.info(f"  SLOW_OPERATION_THRESHOLD: {cls.SLOW_OPERATION_THRESHOLD_SECONDS}s")
        logger.info(f"  MEMORY_CLEANUP_ENABLED: {cls.MEMORY_CLEANUP_ENABLED}")
        logger.info(f"  PERFORMANCE_MONITORING: {cls.PERFORMANCE_MONITORING_ENABLED}")
        logger.info(f"  SCHEDULE_INTERVAL_MINUTES: {cls.SCHEDULE_INTERVAL_MINUTES}")

# Create singleton instance
settings = Settings()