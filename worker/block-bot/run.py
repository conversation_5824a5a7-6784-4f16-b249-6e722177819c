#!/usr/bin/env python3
"""
Production Block Matcher with configurable scheduling and unified logging
Multi-threaded support for parallel execution
"""
import os
import sys
import time
import signal
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import settings and configure logging
from config.settings import settings
import logging

# Configure unified logging system based on settings
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create logger for main application
logger = logging.getLogger('block_matcher_runner')

# Global flag for graceful shutdown
shutdown_requested = False

# Thread-safe logging queue
log_queue = queue.Queue()
log_lock = threading.Lock()

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    global shutdown_requested
    with log_lock:
        logger.info(f"Received signal {signum}, requesting shutdown...")
    shutdown_requested = True

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def run_unified_data_sync():
    """Execute the unified data sync process"""
    thread_id = threading.get_ident()
    try:
        with log_lock:
            logger.info(f"🔄 [Thread-{thread_id}] Starting unified data sync at {datetime.now()}")
        
        # Import and run unified data sync
        from services.unified_data_syncer import sync_all_unified_data
        
        with log_lock:
            logger.info(f"[Thread-{thread_id}] Syncing unified data with batch_size={settings.BATCH_SIZE}")
        
        results = sync_all_unified_data(
            batch_size=settings.get_safe_batch_size(),# Use direct env value
            transaction_days_back=7,   # Reduced from 30 days for better performance
            dispute_days_back=14       # Reduced from 90 days for better performance
        )
        
        total_synced = sum(results.values())
        with log_lock:
            logger.info(f"✅ [Thread-{thread_id}] Unified data sync completed. Synced: {total_synced} records")
            logger.info(f"[Thread-{thread_id}] Sync breakdown: {results}")
        
        return {"task": "unified_data_sync", "success": True, "results": results}
        
    except Exception as e:
        with log_lock:
            logger.error(f"❌ [Thread-{thread_id}] Error in unified data sync: {str(e)}")
            logger.exception("Full traceback:")
        return {"task": "unified_data_sync", "success": False, "error": str(e)}

def run_block_matcher():
    """Execute the block matcher process"""
    thread_id = threading.get_ident()
    try:
        with log_lock:
            logger.info(f"🔄 [Thread-{thread_id}] Starting block matcher run at {datetime.now()}")
        
        # Import and run bulk processing with settings
        from services.block_matcher import process_all_blocks_bulk
        
        with log_lock:
            logger.info(f"[Thread-{thread_id}] Initializing BATCH operations with batch_size={settings.BATCH_SIZE}")
        
        matches = process_all_blocks_bulk(
            limit=None,  # Process all blocks
            batch_size=settings.get_safe_batch_size(),  # Use direct env value
        )
        
        with log_lock:
            logger.info(f"✅ [Thread-{thread_id}] Block matcher run completed. Processed: {matches} matches")
        
        return {"task": "block_matcher", "success": True, "matches": matches}
        
    except Exception as e:
        with log_lock:
            logger.error(f"❌ [Thread-{thread_id}] Error in block matcher run: {str(e)}")
            logger.exception("Full traceback:")
        return {"task": "block_matcher", "success": False, "error": str(e)}

def run_tasks_parallel():
    """Execute unified data sync and block matcher in parallel"""
    start_time = datetime.now()
    with log_lock:
        logger.info(f"🚀 Starting parallel execution at {start_time}")
    
    # Use ThreadPoolExecutor for parallel execution
    with ThreadPoolExecutor(max_workers=2, thread_name_prefix="Worker") as executor:
        try:
            # Submit both tasks concurrently
            future_data_sync = executor.submit(run_unified_data_sync)
            future_block_matcher = executor.submit(run_block_matcher)
            
            # Wait for both tasks to complete
            results = {}
            for future in as_completed([future_data_sync, future_block_matcher]):
                try:
                    result = future.result()
                    results[result["task"]] = result
                    
                    with log_lock:
                        if result["success"]:
                            logger.info(f"✅ {result['task']} completed successfully")
                        else:
                            logger.error(f"❌ {result['task']} failed: {result.get('error', 'Unknown error')}")
                            
                except Exception as e:
                    with log_lock:
                        logger.error(f"❌ Error getting result from future: {str(e)}")
            
            # Summary logging
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            successful_tasks = [task for task, result in results.items() if result.get("success")]
            failed_tasks = [task for task, result in results.items() if not result.get("success")]
            
            with log_lock:
                logger.info(f"🎯 Parallel execution completed in {duration:.2f}s")
                if successful_tasks:
                    logger.info(f"✅ Successful: {', '.join(successful_tasks)}")
                if failed_tasks:
                    logger.error(f"❌ Failed: {', '.join(failed_tasks)}")
            
            return results
            
        except Exception as e:
            with log_lock:
                logger.error(f"❌ Error in parallel execution: {str(e)}")
                logger.exception("Full traceback:")
            return {"error": str(e)}

def run_tasks_sequential():
    """Execute tasks sequentially (fallback mode)"""
    with log_lock:
        logger.info("🔄 Running tasks sequentially (fallback mode)")
    
    # Run unified data sync first, then block matcher
    sync_result = run_unified_data_sync()
    matcher_result = run_block_matcher()
    
    return {
        "unified_data_sync": sync_result,
        "block_matcher": matcher_result
    }

def run_scheduled():
    """Run block matcher on a schedule with configurable intervals using multithreading"""
    setup_signal_handlers()
    
    with log_lock:
        logger.info(f"🚀 Starting Scheduled Block Matcher with Unified Data Sync - {settings.SCHEDULE_INTERVAL_MINUTES}-minute intervals")
        logger.info("⚡ Multithreaded execution enabled for improved performance")
    
    # Log current settings for transparency
    if settings.PERFORMANCE_MONITORING_ENABLED:
        settings.log_settings()
    
    # Determine execution mode - try parallel first, fallback to sequential if needed
    use_parallel = True
    
    # Run first execution immediately
    try:
        if use_parallel:
            results = run_tasks_parallel()
        else:
            results = run_tasks_sequential()
            
        # Check if parallel execution had issues
        if use_parallel and "error" in results:
            with log_lock:
                logger.warning("⚠️  Parallel execution encountered issues, switching to sequential mode")
            use_parallel = False
            results = run_tasks_sequential()
            
    except Exception as e:
        with log_lock:
            logger.error(f"❌ Error in initial execution: {str(e)}")
        use_parallel = False
    
    # Schedule subsequent runs
    while not shutdown_requested:
        try:
            # Sleep for the configured interval (convert minutes to seconds)
            sleep_seconds = settings.SCHEDULE_INTERVAL_MINUTES * 60
            
            with log_lock:
                execution_mode = "parallel" if use_parallel else "sequential"
                logger.info(f"⏰ Next run in {settings.SCHEDULE_INTERVAL_MINUTES} minutes... (Mode: {execution_mode})")
            
            # Sleep in small chunks to allow for responsive shutdown
            for _ in range(sleep_seconds):
                if shutdown_requested:
                    break
                time.sleep(1)
            
            if not shutdown_requested:
                # Execute tasks based on determined mode
                try:
                    if use_parallel:
                        results = run_tasks_parallel()
                        
                        # Check for parallel execution issues and fallback if needed
                        if "error" in results:
                            with log_lock:
                                logger.warning("⚠️  Parallel execution failed, falling back to sequential")
                            use_parallel = False
                            results = run_tasks_sequential()
                    else:
                        results = run_tasks_sequential()
                        
                except Exception as e:
                    with log_lock:
                        logger.error(f"❌ Error in scheduled execution: {str(e)}")
                    # Continue running even if one cycle fails
                
        except KeyboardInterrupt:
            with log_lock:
                logger.info("Scheduler interrupted by user")
            break
    
    with log_lock:
        logger.info("✅ Scheduler stopped gracefully")

def main():
    """Main entry point"""
    with log_lock:
        logger.info("🚀 Starting Production Block Matcher with Unified Data Sync (Multithreaded)...")
    run_scheduled()

if __name__ == "__main__":
    main()