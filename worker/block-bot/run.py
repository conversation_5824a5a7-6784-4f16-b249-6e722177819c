#!/usr/bin/env python3
"""
Production Block Matcher with configurable scheduling and unified logging
"""
import os
import sys
import time
import signal
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import settings and configure logging
from config.settings import settings
import logging

# Configure unified logging system based on settings
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create logger for main application
logger = logging.getLogger('block_matcher_runner')

# Global flag for graceful shutdown
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    global shutdown_requested
    logger.info(f"Received signal {signum}, requesting shutdown...")
    shutdown_requested = True

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def run_block_matcher():
    """Execute the block matcher process"""
    try:
        logger.info(f"🔄 Starting block matcher run at {datetime.now()}")
        
        # Import and run bulk processing with settings
        from services.block_matcher import process_all_blocks_bulk
        
        logger.info(f"Initializing BATCH operations with batch_size={settings.get_safe_batch_size()}")
        
        matches = process_all_blocks_bulk(
            limit=None,  # Process all blocks
            batch_size=None  # Use settings default
        )
        
        logger.info(f"✅ Block matcher run completed. Processed: {matches} matches")
        
    except Exception as e:
        logger.error(f"❌ Error in block matcher run: {str(e)}")
        logger.exception("Full traceback:")

def run_scheduled():
    """Run block matcher on a schedule with configurable intervals"""
    setup_signal_handlers()
    
    logger.info(f"🚀 Starting Scheduled Block Matcher with {settings.SCHEDULE_INTERVAL_MINUTES}-minute intervals")
    
    # Log current settings for transparency
    if settings.PERFORMANCE_MONITORING_ENABLED:
        settings.log_settings()
    
    # Run first execution immediately
    run_block_matcher()
    
    # Schedule subsequent runs
    while not shutdown_requested:
        try:
            # Sleep for the configured interval (convert minutes to seconds)
            sleep_seconds = settings.SCHEDULE_INTERVAL_MINUTES * 60
            
            logger.info(f"⏰ Next run in {settings.SCHEDULE_INTERVAL_MINUTES} minutes...")
            
            # Sleep in small chunks to allow for responsive shutdown
            for _ in range(sleep_seconds):
                if shutdown_requested:
                    break
                time.sleep(1)
            
            if not shutdown_requested:
                run_block_matcher()
                
        except KeyboardInterrupt:
            logger.info("Scheduler interrupted by user")
            break
    
    logger.info("✅ Scheduler stopped gracefully")

def main():
    """Main entry point"""
    logger.info("🚀 Starting Production Block Matcher...")
    run_scheduled()

if __name__ == "__main__":
    main()