-- Test script to verify indexes are working correctly
-- Run this after applying the migration

-- 1. Check if indexes were created successfully
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('shopify_transactions', 'shopify_disputes', 'linked_stores', 'matched_blocks')
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- 2. Test query performance for getUserStoreIds
EXPLAIN (ANALYZE, BUFFERS) 
SELECT id
FROM linked_stores
WHERE user_id = 'test-user-id' AND is_active = true;

-- 3. Test query performance for chargeback data (transactions)
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    DATE(created_at) as date_key,
    COUNT(*) as transaction_count
FROM shopify_transactions 
WHERE linked_store_id IN ('store-1', 'store-2', 'store-3')
    AND created_at >= '2024-01-01'::timestamp
    AND created_at <= '2024-12-31'::timestamp
GROUP BY DATE(created_at)
ORDER BY DATE(created_at);

-- 4. Test query performance for disputes with chargeback filter
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    DATE(created_at) as date_key,
    COUNT(*) as dispute_count
FROM shopify_disputes 
WHERE linked_store_id IN ('store-1', 'store-2', 'store-3')
    AND created_at >= '2024-01-01'::timestamp
    AND created_at <= '2024-12-31'::timestamp
    AND type = 'chargeback'
GROUP BY DATE(created_at)
ORDER BY DATE(created_at);

-- 5. Test matched_blocks user store query
EXPLAIN (ANALYZE, BUFFERS)
SELECT DISTINCT linked_store_id 
FROM matched_blocks 
WHERE user_id = 'test-user-id' AND linked_store_id IS NOT NULL;

-- 6. Check index usage statistics (run this after some real queries)
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE indexname LIKE 'idx_%'
    AND tablename IN ('shopify_transactions', 'shopify_disputes', 'linked_stores', 'matched_blocks')
ORDER BY idx_scan DESC;

-- 7. Show table sizes and index sizes
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE tablename IN ('shopify_transactions', 'shopify_disputes', 'linked_stores', 'matched_blocks')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;