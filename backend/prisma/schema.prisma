generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider          = "postgresql"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model User {
  id           String        @id @default(uuid())
  code         String        @unique
  fullName     String        @map("full_name")
  phoneNumber  String?       @map("phone_number")
  email        String        @unique
  password     String
  address      String?
  gender       String
  birthDate    DateTime      @map("birth_date") @db.Timestamptz
  status       Boolean       @default(true)
  note         String?
  isActive     Boolean       @default(true) @map("is_active")
  uninstalledAt DateTime?    @map("uninstalled_at") @db.Timestamptz
  createdAt    DateTime      @default(now()) @map("created_at") @db.Timestamptz
  updatedAt    DateTime      @updatedAt @map("updated_at") @db.Timestamptz
  linkedStores LinkedStore[]
  tokens       Token[]
  matchedBlocks MatchedBlock[]

  @@index([isActive])
  @@map("users")
}

model Client {
  id          String   @id @default(uuid())
  name        String
  email       String   @unique
  description String?
  APIKey      String   @unique @map("api_key")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz

  @@map("clients")
}

model Token {
  id           String   @id @default(uuid())
  userId       String   @map("user_id")
  token        String   @unique
  refreshToken String?  @unique @map("refresh_token")
  userAgent    String?  @map("user_agent")
  ipAddress    String?  @map("ip_address")
  expiresAt    DateTime @map("expires_at") @db.Timestamptz
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tokens")
}

model LinkedStore {
  id              String        @id @default(uuid())
  userId          String        @map("user_id")
  storeName       String        @map("store_name")
  provider        String
  providerStoreId String?       @map("provider_store_id")
  data            Json
  isActive        Boolean       @default(true) @map("is_active")
  uninstalledAt   DateTime?     @map("uninstalled_at") @db.Timestamptz
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  alertInfos      AlertInfo[]   @relation("StoreAlerts")
  stripeAccount   StripeAccount? @relation("StoreStripeAccount")
  orders          Order[]       @relation("StoreOrders")
  shopifyOrders   ShopifyOrder[]
  shopifyTransactions ShopifyTransaction[]
  shopifyPayouts  ShopifyPayout[]
  shopifyDisputes ShopifyDispute[]
  mappedBlocks    MappedBlock[]
  matchedBlocks   MatchedBlock[]
  bills           Bill[]
  paymentHistories PaymentHistory[]
  blockRefunds    BlockRefund[]
  blockUsages     BlockUsage[]
  paypalAgreements PayPalAgreement[]
  storePlan       StorePlan?
  usageTracking   UsageTracking[]
  shopifySubscription ShopifySubscription? @relation("StoreShopifySubscription")
  stripePaymentIntents StripePaymentIntent[]
  stripeCharges       StripeCharge[]
  stripeDisputes      StripeDispute[]
  
  @@unique([provider, providerStoreId])
  @@index([isActive])
  @@index([providerStoreId, isActive])
  @@map("linked_stores")
}

model StoreSettings {
  id              String      @id @default(uuid())
  storeId         String      @map("store_id")
  name            String      // Setting name (e.g., "monthly_goal", "currency", etc.)
  value           String      // Setting value (stored as string, can be parsed as needed)
  createdAt       DateTime    @default(now()) @map("created_at") @db.Timestamptz
  updatedAt       DateTime    @updatedAt @map("updated_at") @db.Timestamptz

  @@unique([storeId, name])
  @@map("store_settings")
}



model Block {
  id                      String    @id
  alertId                 String    @map("alert_id")
  alertTime               DateTime  @map("alert_time") @db.Timestamptz
  alertType               String    @map("alert_type")
  amount                  Int
  currency                String
  descriptor              String
  authCode                String?   @map("auth_code")
  cardBin                 String?   @map("card_bin")
  cardNumber              String?   @map("card_number")
  chargebackCode          String?   @map("chargeback_code")
  disputeAmount           Int?      @map("dispute_amount")
  disputeCurrency         String?   @map("dispute_currency")
  transactionTime         DateTime? @map("transaction_time") @db.Timestamptz
  age                     String?
  alertSource             String?   @map("alert_source")
  arn                     String?
  issuer                  String?
  initiatedBy             String?   @map("initiated_by")
  liability               String?
  merchantCategoryCode    String?   @map("merchant_category_code")
  transactionId           String?   @map("transaction_id")
  transactionType         String?   @map("transaction_type")
  acquirerBin             String?   @map("acquirer_bin")
  acquirerReferenceNumber String?   @map("acquirer_reference_number")
  alertStatus             String?   @map("alert_status")
  caid                    String?
  descriptorContact       String?   @map("descriptor_contact")
  ruleName                String?   @map("rule_name")
  ruleType                String?   @map("rule_type")
  type                    String    @default("ETHOCA")
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")
  feedbackData            Json?     @map("feedback_data")
  feedbackStatus          String    @default("PENDING") @map("feedback_status")
  feedbackTime            DateTime? @map("feedback_time") @db.Timestamptz
  mappedBlocks            MappedBlock[]
  matchedBlocks           MatchedBlock[]
  blockRefunds            BlockRefund[]
  blockUsages             BlockUsage[]

  @@map("blocks")
}

model AlertInfo {
  id                    String      @id @default(uuid())
  alertType             String      @map("alert_type")   // "RDR" or "ETHOCA"
  descriptor            String    
  bin                   String?     
  caid                  String?     
  arn                   String?     // Acquirer Reference Number
  registrationStatus    String      @default("WAITING") @map("registration_status") // "WAITING", "EFFECTED", "CLOSING", "CLOSED"
  registrationMessage   String?     @map("registration_message")
  registeredAt          DateTime?   @map("registered_at") @db.Timestamptz
  closedAt              DateTime?   @map("closed_at") @db.Timestamptz
  storeId               String      @map("store_id")
  createdAt             DateTime    @default(now()) @map("created_at")
  updatedAt             DateTime    @updatedAt @map("updated_at")
  store                 LinkedStore @relation("StoreAlerts", fields: [storeId], references: [id], onDelete: Cascade)

  @@map("alert_infos")
}

model StripeAccount {
  id                String       @id @default(uuid())
  storeId           String       @unique @map("store_id")
  stripeAccountId   String?      @map("stripe_account_id")
  status            String       @default("pending") // "pending", "active", "inactive"
  stripeCustomerId  String?      @map("stripe_customer_id")
  paymentMethodId   String?      @map("payment_method_id")
  cardLast4         String?      @map("card_last4")
  cardBrand         String?      @map("card_brand")
  billingEmail      String?      @map("billing_email")
  cardholderName    String?      @map("cardholder_name")
  cardCountry       String?      @map("card_country")
  setupCompleted    Boolean      @default(false) @map("setup_completed")
  createdAt         DateTime     @default(now()) @map("created_at")
  updatedAt         DateTime     @updatedAt @map("updated_at")
  store             LinkedStore  @relation("StoreStripeAccount", fields: [storeId], references: [id], onDelete: Cascade)

  @@index([stripeAccountId])
  @@map("stripe_accounts")
}

model Bill {
  id               String         @id @default(uuid())
  linkedStoreId    String         @map("linked_store_id")
  amount           Int
  currency         String
  description      String?
  status           BillStatus     @default(PENDING)
  dueDate          DateTime       @map("due_date") @db.Timestamptz
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")
  linkedStore      LinkedStore    @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  paymentHistories PaymentHistory[]
  paypalPayments   PayPalPayment[]

  @@map("bills")
}

model PaymentHistory {
  id               String         @id @default(uuid())
  linkedStoreId    String         @map("linked_store_id")
  billId           String         @map("bill_id")
  amount           Int
  currency         String
  description      String?
  status           PaymentStatus
  paymentDate      DateTime       @map("payment_date") @db.Timestamptz
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")
  linkedStore      LinkedStore    @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  bill             Bill           @relation(fields: [billId], references: [id], onDelete: Cascade)

  @@map("payment_history")
}

enum FeedbackStatus {
  PENDING
  SENT
  FAILED
}

enum BillStatus {
  PENDING
  PAID
  FAILED
  CANCELLED
}

enum PaymentStatus {
  SUCCEEDED
  FAILED
  PENDING
  REFUNDED
}

model Order {
  id              String      @id @default(uuid())
  storeId         String      @map("store_id")
  orderId         String      @map("order_id")  // External order ID from the store system
  customerName    String?     @map("customer_name")
  customerEmail   String?     @map("customer_email")
  amount          Float
  currency        String
  status          String      // "pending", "completed", "cancelled", "refunded"
  items           Json?       // Array of ordered items
  shippingAddress Json?       @map("shipping_address")
  paymentMethod   String?     @map("payment_method")
  paymentDetails  Json?       @map("payment_details")
  metadata        Json?       // Additional order-related data
  createdAt       DateTime    @default(now()) @map("created_at") @db.Timestamptz
  updatedAt       DateTime    @updatedAt @map("updated_at") @db.Timestamptz
  store           LinkedStore @relation("StoreOrders", fields: [storeId], references: [id], onDelete: Cascade)

  @@map("orders")
}

model ShopifyOrder {
  id                         BigInt             @id
  linkedStoreId              String             @map("linked_store_id")
  name                       String?
  note                       String?
  tags                       String?
  test                       Boolean?
  email                      String?
  phone                      String?
  token                      String?
  appId                      Int?               @map("app_id")
  number                     Int?
  orderNumber                Int?               @map("order_number")
  currency                   String?
  customerData                Json?              @map("customer_data")     // Customer info as JSON
  customer                   Json?              // Full customer object
  closedAt                   DateTime?          @map("closed_at") @db.Timestamptz
  confirmed                  Boolean?
  confirmationNumber         String?            @map("confirmation_number")
  deviceId                   String?            @map("device_id")
  poNumber                   String?            @map("po_number")
  reference                  String?
  cancelReason               String?            @map("cancel_reason")
  cancelledAt                DateTime?          @map("cancelled_at") @db.Timestamptz
  taxData                    Json?              @map("tax_data")         // Tax line information
  taxLines                   Json?              @map("tax_lines")       // Tax lines
  totalTax                   Int?               @map("total_tax")
  totalTaxSet                Json?              @map("total_tax_set")
  browserIp                  String?            @map("browser_ip")
  cartToken                  String?            @map("cart_token")
  checkoutToken              String?            @map("checkout_token")
  clientDetails              Json?              @map("client_details")
  company                    Json?
  createdAt                  DateTime?          @map("created_at") @db.Timestamptz
  updatedAt                  DateTime?          @map("updated_at") @db.Timestamptz
  processedAt                DateTime?          @map("processed_at") @db.Timestamptz
  lineItems                  Json?              @map("line_items")       // Line items as JSON
  discountApplications       Json?              @map("discount_applications")
  discountCodes              Json?              @map("discount_codes")
  sourceUrl                  String?            @map("source_url")
  sourceIdentifier           String?            @map("source_identifier")
  sourceName                 String?            @map("source_name")
  taxExempt                  Boolean?           @map("tax_exempt")
  checkoutId                 BigInt?            @map("checkout_id")
  locationId                 BigInt?            @map("location_id")
  merchantBusinessEntityId   String?            @map("merchant_business_entity_id")
  merchantOfRecordAppId      BigInt?            @map("merchant_of_record_app_id")
  totalPrice                 Int?               @map("total_price")
  totalPriceSet              Json?              @map("total_price_set")
  currentTotalPrice          Int?               @map("current_total_price")
  currentTotalPriceSet       Json?              @map("current_total_price_set")
  subtotalPrice              Int?               @map("subtotal_price")
  subtotalPriceSet           Json?              @map("subtotal_price_set")
  currentSubtotalPrice       Int?               @map("current_subtotal_price")
  currentSubtotalPriceSet    Json?              @map("current_subtotal_price_set")
  totalDiscounts             Int?               @map("total_discounts")
  totalDiscountsSet          Json?              @map("total_discounts_set")
  currentTotalDiscounts      Int?               @map("current_total_discounts")
  currentTotalDiscountsSet   Json?              @map("current_total_discounts_set")
  totalLineItemsPrice        Int?               @map("total_line_items_price")
  totalLineItemsPriceSet     Json?              @map("total_line_items_price_set")
  totalShippingPriceSet      Json?              @map("total_shipping_price_set")
  totalOutstanding           Int?               @map("total_outstanding")
  totalTipReceived           Int?               @map("total_tip_received")
  totalWeight                Int?               @map("total_weight")
  fulfillments               Json?              // Fulfillments as JSON
  fulfillmentStatus          String?            @map("fulfillment_status")
  landingSite                String?            @map("landing_site")
  landingSiteRef             String?            @map("landing_site_ref")
  referringSite              String?            @map("referring_site")
  contactEmail               String?            @map("contact_email")
  paymentTerms               Json?              @map("payment_terms")
  paymentGatewayNames        Json?              @map("payment_gateway_names")
  shippingLines              Json?              @map("shipping_lines")    // Shipping lines as JSON
  taxesIncluded              Boolean?           @map("taxes_included")
  billingAddress             Json?              @map("billing_address")   // Billing address as JSON
  shippingAddress            Json?              @map("shipping_address")  // Shipping address as JSON
  customerLocale             String?            @map("customer_locale")
  buyerAcceptsMarketing      Boolean?           @map("buyer_accepts_marketing")
  refunds                    Json?
  dutiesIncluded             Boolean?           @map("duties_included")
  currentTotalDutiesSet      Json?              @map("current_total_duties_set")
  originalTotalDutiesSet     Json?              @map("original_total_duties_set")
  estimatedTaxes             Boolean?           @map("estimated_taxes")
  noteAttributes             Json?              @map("note_attributes")   // Note attributes as JSON
  financialStatus            String?            @map("financial_status")
  orderStatusUrl             Json?              @map("order_status_url")
  currentTotalAdditionalFeesSet Json?           @map("current_total_additional_fees_set")
  originalTotalAdditionalFeesSet Json?          @map("original_total_additional_fees_set")
  totalCashRoundingPaymentAdjustmentSet Json?  @map("total_cash_rounding_payment_adjustment_set")
  totalCashRoundingRefundAdjustmentSet Json?   @map("total_cash_rounding_refund_adjustment_set")
  userId                     BigInt?            @map("user_id")
  linkedStore                LinkedStore        @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  transactions                ShopifyTransaction[]
  mappedBlocks               MappedBlock[]
  blockRefunds               BlockRefund[]

  @@map("shopify_orders")
}

model ShopifyTransaction {
  id                         BigInt          @id
  linkedStoreId             String          @map("linked_store_id")
  orderId                   BigInt?         @map("order_id")
  payoutId                  BigInt?         @map("payout_id")
  fee                       Int?
  net                       Int?
  test                      Boolean?
  type                      String?
  amount                    Int?
  amountRounding            Int?            @map("amount_rounding")
  authorization             String?
  authorizationExpiresAt    DateTime?       @map("authorization_expires_at") @db.Timestamptz
  currency                  String?
  deviceId                  BigInt?         @map("device_id")
  errorCode                 String?         @map("error_code")
  extendedAuthorizationAttributes Json?     @map("extended_authorization_attributes")
  gateway                   String?
  kind                      String?
  locationId                Json?           @map("location_id")
  message                   String?
  parentId                  BigInt?         @map("parent_id")
  paymentDetails           Json?           @map("payment_details")
  paymentsRefundAttributes Json?           @map("payments_refund_attributes")
  processedAt               DateTime?       @map("processed_at") @db.Timestamptz
  receipt                   Json?
  sourceName                String?         @map("source_name")
  status                    String?
  totalUnsettledSet         Json?           @map("total_unsettled_set")
  userId                    BigInt?         @map("user_id")
  currencyExchangeAdjustment Json?        @map("currency_exchange_adjustment")
  manualPaymentGateway      Boolean?        @map("manual_payment_gateway")
  sourceId                  BigInt?         @map("source_id")
  sourceType                String?         @map("source_type")
  payoutStatus              String?         @map("payout_status")
  sourceOrderId             BigInt?         @map("source_order_id")
  adjustmentReason          String?         @map("adjustment_reason")
  sourceOrderTransactionId  BigInt?         @map("source_order_transaction_id")
  adjustmentOrderTransactions Json?         @map("adjustment_order_transactions")
  referenceCardNumber       String?         @map("reference_card_number")
  referenceAmount           Int?            @map("reference_amount")
  referenceTransactionTime  DateTime?       @map("reference_transaction_time") @db.Timestamptz
  referenceCurrency         String?         @map("reference_currency")
  referenceArn              String?         @map("reference_arn")
  referenceAuthorizationCode String?        @map("reference_authorization_code")
  createdAt                 DateTime        @default(now()) @map("created_at") @db.Timestamptz
  updatedAt                 DateTime        @updatedAt @map("updated_at") @db.Timestamptz
  linkedStore                LinkedStore    @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  order                     ShopifyOrder?   @relation(fields: [orderId], references: [id])
  payout                    ShopifyPayout?  @relation(fields: [payoutId], references: [id])
  disputes                  ShopifyDispute[]
  mappedBlocks              MappedBlock[]

  @@map("shopify_transactions")
}

model ShopifyPayout {
  id                BigInt              @id
  linkedStoreId     String              @map("linked_store_id")
  date              DateTime? @db.Timestamptz
  amount            Int?
  status            String?
  summary           Json?
  currency          String?
  createdAt         DateTime            @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime            @updatedAt @map("updated_at") @db.Timestamptz
  linkedStore       LinkedStore         @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  transactions      ShopifyTransaction[]

  @@map("shopify_payouts")
}

model ShopifyDispute {
  id                BigInt            @id
  linkedStoreId     String            @map("linked_store_id")
  transactionId     BigInt?           @map("transaction_id")
  type              String?
  amount            Int?
  reason            String?
  status            String?
  currency          String?
  orderId           BigInt?           @map("order_id")
  finalizedOn       DateTime?         @map("finalized_on") @db.Timestamptz
  initiatedAt       DateTime?         @map("initiated_at") @db.Timestamptz
  evidenceDueBy     DateTime?         @map("evidence_due_by") @db.Timestamptz
  evidenceSentOn    DateTime?         @map("evidence_sent_on") @db.Timestamptz
  networkReasonCode String?           @map("network_reason_code")
  createdAt         DateTime          @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime          @updatedAt @map("updated_at") @db.Timestamptz
  linkedStore       LinkedStore       @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  transaction       ShopifyTransaction? @relation(fields: [transactionId], references: [id])

  @@map("shopify_disputes")
}

model MappedBlock {
  id              String             @id @default(uuid())
  blockId         String             @map("block_id")
  linkedStoreId   String             @map("linked_store_id")
  orderId         BigInt?            @map("order_id")
  transactionId   BigInt?            @map("transaction_id")
  createdAt       DateTime           @default(now()) @map("created_at") @db.Timestamptz
  updatedAt       DateTime           @updatedAt @map("updated_at") @db.Timestamptz
  block           Block              @relation(fields: [blockId], references: [id], onDelete: Cascade)
  linkedStore     LinkedStore        @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  order           ShopifyOrder?      @relation(fields: [orderId], references: [id])
  transaction     ShopifyTransaction? @relation(fields: [transactionId], references: [id])

  @@unique([blockId, linkedStoreId, orderId, transactionId])
  @@map("mapped_blocks")
}

model MatchedBlock {
  id                      String             @id // Same as Block ID
  userId                  String             @map("user_id")
  linkedStoreId           String             @map("linked_store_id")
  provider                String             // "shopify", "stripe", etc.
  
  // Matched transaction data - flexible JSON structure to store matched orders/transactions
  matchedData             Json?              @map("matched_data")  // Store matched transactions/orders info
  
  // Block data fields (copied from blocks table)
  alertId                 String             @map("alert_id")
  alertTime               DateTime           @map("alert_time") @db.Timestamptz
  alertType               String             @map("alert_type")
  amount                  Int
  currency                String
  descriptor              String
  authCode                String?            @map("auth_code")
  cardBin                 String?            @map("card_bin")
  cardNumber              String?            @map("card_number")
  chargebackCode          String?            @map("chargeback_code")
  disputeAmount           Int?               @map("dispute_amount")
  disputeCurrency         String?            @map("dispute_currency")
  transactionTime         DateTime?          @map("transaction_time") @db.Timestamptz
  age                     String?
  alertSource             String?            @map("alert_source")
  arn                     String?
  issuer                  String?
  initiatedBy             String?            @map("initiated_by")
  liability               String?
  merchantCategoryCode    String?            @map("merchant_category_code")
  transactionId_Block     String?            @map("transaction_id")
  transactionType         String?            @map("transaction_type")
  acquirerBin             String?            @map("acquirer_bin")
  acquirerReferenceNumber String?            @map("acquirer_reference_number")
  alertStatus             String?            @map("alert_status")
  caid                    String?
  descriptorContact       String?            @map("descriptor_contact")
  ruleName                String?            @map("rule_name")
  ruleType                String?            @map("rule_type")
  type                    String             @default("ETHOCA")
  feedbackData            Json?              @map("feedback_data")
  feedbackStatus          String             @default("PENDING") @map("feedback_status")
  feedbackTime            DateTime?          @map("feedback_time") @db.Timestamptz
  
  createdAt               DateTime           @default(now()) @map("created_at") @db.Timestamptz
  updatedAt               DateTime           @updatedAt @map("updated_at") @db.Timestamptz
  
  // Relations
  user                    User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  block                   Block              @relation(fields: [id], references: [id], onDelete: Cascade)
  linkedStore             LinkedStore        @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)

  @@unique([id, linkedStoreId, provider])
  @@index([userId])
  @@index([linkedStoreId])
  @@index([provider])
  @@index([descriptor])
  @@index([type])
  @@index([alertTime])
  @@index([feedbackStatus])
  @@index([cardBin])
  @@index([caid])
  @@index([amount])
  @@map("matched_blocks")
}

model BlockRefund {
  id                String          @id @default(uuid())
  blockId           String          @map("block_id")
  storeId           String          @map("store_id")
  orderId           BigInt          @map("order_id")
  refundId          BigInt?         @map("refund_id")         // Shopify refund ID
  amount            Int
  currency          String
  reason            String?
  note              String?
  notify            Boolean         @default(true)
  shipping          Json?                                       // Shipping refund details
  refundLineItems   Json?           @map("refund_line_items")  // Line items to refund
  transactions      Json?                                       // Refund transactions
  orderAdjustments  Json?           @map("order_adjustments")  // Order adjustments
  duties            Json?                                       // Duties to refund
  gateway           String?
  parentId          BigInt?         @map("parent_id")          // Parent refund ID if applicable
  processedAt       DateTime?       @map("processed_at") @db.Timestamptz
  restock           Boolean         @default(true)
  userId            BigInt?         @map("user_id")            // Shopify user who processed refund
  adminGraphqlApiId String?         @map("admin_graphql_api_id")
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")
  block             Block           @relation(fields: [blockId], references: [id], onDelete: Cascade)
  linkedStore       LinkedStore     @relation(fields: [storeId], references: [id], onDelete: Cascade)
  shopifyOrder      ShopifyOrder    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("block_refunds")
}

model BlockUsage {
  id        String         @id @default(uuid())
  storeId   String         @map("store_id")
  blockId   String         @map("block_id")
  type      String         // Type của block (ETHOCA, RDR, etc.)
  time      DateTime       @default(now()) @db.Timestamptz // Thời điểm tạo record
  status    FeedbackStatus @default(PENDING) // Feedback status của block
  createdAt DateTime       @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime       @updatedAt @map("updated_at") @db.Timestamptz
  store     LinkedStore    @relation(fields: [storeId], references: [id], onDelete: Cascade)
  block     Block          @relation(fields: [blockId], references: [id], onDelete: Cascade)

  @@map("block_usages")
}

model PayPalAgreement {
  id              String      @id @default(uuid())
  linkedStoreId   String      @map("linked_store_id")
  agreementId     String      @unique @map("agreement_id") // PayPal agreement ID
  status          String      // ACTIVE, CANCELLED, SUSPENDED
  data            Json?       // Store vault ID and other metadata
  createdAt       DateTime    @default(now()) @map("created_at") @db.Timestamptz
  updatedAt       DateTime    @updatedAt @map("updated_at") @db.Timestamptz
  
  linkedStore     LinkedStore @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  payments        PayPalPayment[]
  
  @@map("paypal_agreements")
}

model PayPalPayment {
  id          String   @id @default(uuid())
  agreementId String   @map("agreement_id")
  billId      String?  @map("bill_id")
  paymentId   String   @unique @map("payment_id") // PayPal payment ID
  amount      Int      // Amount in cents (multiply by 100)
  currency    String   @default("USD")
  status      String   // COMPLETED, FAILED, PENDING
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz
  
  agreement   PayPalAgreement @relation(fields: [agreementId], references: [id], onDelete: Cascade)
  bill        Bill?            @relation(fields: [billId], references: [id])
  
  @@map("paypal_payments")
}

model StorePlan {
  id          String   @id @default(uuid())
  storeId     String   @unique @map("store_id")
  planTier    String   @map("plan_tier") // free_trial, starter, growth, scale
  blockLimit  Int      @map("block_limit") // Monthly block limit
  startDate   DateTime @map("start_date") @db.Timestamptz
  endDate     DateTime? @map("end_date") @db.Timestamptz
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz
  
  linkedStore LinkedStore @relation(fields: [storeId], references: [id], onDelete: Cascade)
  
  @@map("store_plans")
}

model UsageTracking {
  id          String   @id @default(uuid())
  storeId     String   @map("store_id")
  blockType   String   @map("block_type") // ETHOCA, RDR, TOPUP
  period      String   // YYYY-MM format
  timestamp   DateTime @db.Timestamptz
  amount      Int?     @map("amount") // Amount in cents (used for TOPUP type)
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz
  
  linkedStore LinkedStore @relation(fields: [storeId], references: [id], onDelete: Cascade)
  
  @@index([storeId, period])
  @@map("usage_tracking")
}

model ShopifySubscription {
  id                    String      @id @default(uuid())
  linkedStoreId         String      @unique @map("linked_store_id") // One subscription per store
  shopifySubscriptionId String?     @unique @map("shopify_subscription_id") // Shopify's subscription ID
  shopifyChargeId       String?     @unique @map("shopify_charge_id") // Shopify's charge ID  
  planName              String      @map("plan_name") @default("Professional Plan")
  planType              String      @map("plan_type") @default("USAGE_BASED") // USAGE_BASED, TIME_BASED, HYBRID
  status                String      @default("PENDING") // PENDING, ACTIVE, CANCELLED, EXPIRED, DECLINED, FROZEN
  amount                Int         @default(0) // Base amount in cents ($0.00)
  currency              String      @default("USD")
  billingInterval       String      @map("billing_interval") @default("EVERY_30_DAYS") // EVERY_30_DAYS, ANNUAL
  cappedAmount          Int         @map("capped_amount") @default(80000) // $800.00 in cents
  currentUsage          Int         @map("current_usage") @default(0) // Current usage amount in cents
  trialDays             Int?        @map("trial_days") // Free trial period in days
  confirmationUrl       String?     @map("confirmation_url") // Shopify confirmation URL
  returnUrl             String?     @map("return_url") // Return URL after approval
  activatedAt           DateTime?   @map("activated_at") @db.Timestamp(3)
  lastBilledAt          DateTime?   @map("last_billed_at") @db.Timestamp(3)
  nextBillingAt         DateTime?   @map("next_billing_at") @db.Timestamp(3)
  cancelledAt           DateTime?   @map("cancelled_at") @db.Timestamp(3)
  metadata              Json?       // Additional subscription data
  webhookData           Json?       @map("webhook_data") // Latest webhook payload
  createdAt             DateTime    @default(now()) @map("created_at") @db.Timestamp(3)
  updatedAt             DateTime    @updatedAt @map("updated_at") @db.Timestamp(3)
  
  linkedStore           LinkedStore @relation("StoreShopifySubscription", fields: [linkedStoreId], references: [id], onDelete: Cascade)
  usageRecords          ShopifyUsageRecord[]
  
  @@index([shopifySubscriptionId])
  @@index([status])
  @@index([linkedStoreId, status])
  @@map("shopify_subscriptions")
}

model ShopifyUsageRecord {
  id                String              @id @default(uuid())
  subscriptionId    String              @map("subscription_id")
  shopifyRecordId   String?             @unique @map("shopify_record_id") // Shopify's usage record ID
  description       String              // Usage description (e.g., "Block processing for January 2024")
  quantity          Int                 @default(1) // Quantity used
  price             Int                 // Price per unit in cents
  totalAmount       Int                 @map("total_amount") // Total amount for this record in cents
  billingDate       DateTime            @map("billing_date") @db.Timestamp(3) // When this usage should be billed
  metadata          Json?               // Additional usage data (block details, etc.)
  status            String              @default("PENDING") // PENDING, BILLED, FAILED
  createdAt         DateTime            @default(now()) @map("created_at") @db.Timestamp(3)
  updatedAt         DateTime            @updatedAt @map("updated_at") @db.Timestamp(3)
  
  subscription      ShopifySubscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  
  @@index([subscriptionId])
  @@index([billingDate])
  @@index([status])
  @@map("shopify_usage_records")
}

model StripePaymentIntent {
  id                        String    @id
  linkedStoreId            String    @map("linked_store_id")
  object                   String    @default("payment_intent")
  amount                   BigInt
  amountCapturable         BigInt?   @map("amount_capturable")
  amountReceived           BigInt?   @map("amount_received")
  application              String?
  applicationFeeAmount     BigInt?   @map("application_fee_amount")
  automaticPaymentMethods  Json?     @map("automatic_payment_methods")
  canceledAt               DateTime? @map("canceled_at") @db.Timestamptz
  cancellationReason       String?   @map("cancellation_reason")
  captureMethod            String?   @map("capture_method")
  clientSecret             String?   @map("client_secret")
  confirmationMethod       String?   @map("confirmation_method")
  createdAt                DateTime  @map("created_at") @db.Timestamptz
  currency                 String
  customerId               String?   @map("customer_id")
  description              String?
  invoiceId                String?   @map("invoice_id")
  lastPaymentError         Json?     @map("last_payment_error")
  latestChargeId           String?   @map("latest_charge_id")
  livemode                 Boolean
  metadata                 Json?
  nextAction               Json?     @map("next_action")
  onBehalfOf               String?   @map("on_behalf_of")
  paymentMethodId          String?   @map("payment_method_id")
  paymentMethodConfigurationDetails Json? @map("payment_method_configuration_details")
  paymentMethodOptions     Json?     @map("payment_method_options")
  paymentMethodTypes       String[]  @map("payment_method_types")
  processing               Json?
  receiptEmail             String?   @map("receipt_email")
  review                   String?
  setupFutureUsage         String?   @map("setup_future_usage")
  shipping                 Json?
  source                   String?
  statementDescriptor      String?   @map("statement_descriptor")
  statementDescriptorSuffix String?  @map("statement_descriptor_suffix")
  status                   String
  transferData             Json?     @map("transfer_data")
  transferGroup            String?   @map("transfer_group")
  updatedAt                DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  linkedStore LinkedStore @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  charges     StripeCharge[]

  @@index([linkedStoreId])
  @@index([status])
  @@index([createdAt])
  @@index([customerId])
  @@index([latestChargeId])
  @@map("stripe_payment_intents")
}

model StripeCharge {
  id                           String    @id
  linkedStoreId               String    @map("linked_store_id")
  object                      String    @default("charge")
  amount                      BigInt
  amountCaptured              BigInt?   @map("amount_captured")
  amountRefunded              BigInt?   @map("amount_refunded")
  application                 String?
  applicationFee              String?   @map("application_fee")
  applicationFeeAmount        BigInt?   @map("application_fee_amount")
  balanceTransaction          String?   @map("balance_transaction")
  billingDetails              Json?     @map("billing_details")
  calculatedStatementDescriptor String? @map("calculated_statement_descriptor")
  captured                    Boolean
  createdAt                   DateTime  @map("created_at") @db.Timestamptz
  currency                    String
  customerId                  String?   @map("customer_id")
  description                 String?
  destination                 String?
  disputeId                   String?   @map("dispute_id")
  disputed                    Boolean   @default(false)
  failureCode                 String?   @map("failure_code")
  failureMessage              String?   @map("failure_message")
  fraudDetails                Json?     @map("fraud_details")
  invoiceId                   String?   @map("invoice_id")
  livemode                    Boolean
  metadata                    Json?
  onBehalfOf                  String?   @map("on_behalf_of")
  orderId                     String?   @map("order_id")
  outcome                     Json?
  paid                        Boolean
  paymentIntentId             String?   @map("payment_intent_id")
  paymentMethod               String?   @map("payment_method")
  paymentMethodDetails        Json?     @map("payment_method_details")
  receiptEmail                String?   @map("receipt_email")
  receiptNumber               String?   @map("receipt_number")
  receiptUrl                  String?   @map("receipt_url")
  refunded                    Boolean   @default(false)
  refunds                     Json?
  review                      String?
  shipping                    Json?
  source                      Json?
  sourceTransfer              String?   @map("source_transfer")
  statementDescriptor         String?   @map("statement_descriptor")
  statementDescriptorSuffix   String?   @map("statement_descriptor_suffix")
  status                      String
  transferData                Json?     @map("transfer_data")
  transferGroup               String?   @map("transfer_group")
  referenceCardNumber         String?   @map("reference_card_number")
  referenceCreatedAt          DateTime? @map("reference_created_at") @db.Timestamptz
  referenceAmount             BigInt?   @map("reference_amount")
  referenceCurrency           String?   @map("reference_currency")
  updatedAt                   DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  linkedStore     LinkedStore @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  paymentIntent   StripePaymentIntent? @relation(fields: [paymentIntentId], references: [id])
  disputes        StripeDispute[]

  @@index([linkedStoreId])
  @@index([status])
  @@index([createdAt])
  @@index([customerId])
  @@index([paymentIntentId])
  @@index([disputed])
  @@index([refunded])
  @@map("stripe_charges")
}

model StripeDispute {
  id                    String    @id
  linkedStoreId        String    @map("linked_store_id")
  object               String    @default("dispute")
  amount               BigInt
  balanceTransactions  Json?     @map("balance_transactions")
  chargeId             String    @map("charge_id")
  createdAt            DateTime  @map("created_at") @db.Timestamptz
  currency             String
  evidence             Json?
  evidenceDetails      Json?     @map("evidence_details")
  isChargeRefundable   Boolean   @map("is_charge_refundable")
  livemode             Boolean
  metadata             Json?
  networkReasonCode    String?   @map("network_reason_code")
  reason               String
  status               String
  updatedAt            DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  linkedStore LinkedStore   @relation(fields: [linkedStoreId], references: [id], onDelete: Cascade)
  charge      StripeCharge  @relation(fields: [chargeId], references: [id])

  @@index([linkedStoreId])
  @@index([status])
  @@index([createdAt])
  @@index([chargeId])
  @@index([reason])
  @@map("stripe_disputes")
}
