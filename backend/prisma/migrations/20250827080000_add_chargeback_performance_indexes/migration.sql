-- CreateIndex
-- Performance optimization indexes for chargeback data queries

-- Shopify Transactions indexes for faster lookups by store and date
CREATE INDEX IF NOT EXISTS "idx_shopify_transactions_store_created" ON "shopify_transactions"("linked_store_id", "created_at");
CREATE INDEX IF NOT EXISTS "idx_shopify_transactions_created_store" ON "shopify_transactions"("created_at", "linked_store_id");

-- Shopify Disputes indexes optimized for chargeback type filtering
CREATE INDEX IF NOT EXISTS "idx_shopify_disputes_store_created_type" ON "shopify_disputes"("linked_store_id", "created_at", "type") WHERE "type" = 'chargeback';
CREATE INDEX IF NOT EXISTS "idx_shopify_disputes_created_store_type" ON "shopify_disputes"("created_at", "linked_store_id", "type") WHERE "type" = 'chargeback';

-- Additional index for disputes without partial WHERE clause (for broader queries)
CREATE INDEX IF NOT EXISTS "idx_shopify_disputes_store_created_type_all" ON "shopify_disputes"("linked_store_id", "created_at", "type");

-- Matched Blocks indexes for user store relationships
CREATE INDEX IF NOT EXISTS "idx_matched_blocks_user_store" ON "matched_blocks"("user_id", "linked_store_id");
CREATE INDEX IF NOT EXISTS "idx_matched_blocks_user_id" ON "matched_blocks"("user_id");

-- Covering index for better performance when selecting linked_store_id by user_id
CREATE INDEX IF NOT EXISTS "idx_matched_blocks_user_covering" ON "matched_blocks"("user_id") INCLUDE ("linked_store_id") WHERE "linked_store_id" IS NOT NULL;

-- LinkedStore index for active stores by user (optimizes getUserStoreIds)
CREATE INDEX IF NOT EXISTS "idx_linked_stores_user_active" ON "linked_stores"("user_id", "is_active") WHERE "is_active" = true;
CREATE INDEX IF NOT EXISTS "idx_linked_stores_user_id" ON "linked_stores"("user_id");