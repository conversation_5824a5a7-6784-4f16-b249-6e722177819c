/**
 * Performance Test Script
 * Compare performance between old $queryRaw methods and new Prisma ORM methods
 */

import { performance } from 'perf_hooks';
import MatchedBlocksService from './matched-blocks.service';
import { TimeRange } from './matched-blocks.service';

interface PerformanceResult {
  method: string;
  timeRange: string;
  duration: number;
  recordCount: number;
  error?: string;
}

class PerformanceTest {
  private static results: PerformanceResult[] = [];

  /**
   * Test chargeback rate chart performance
   */
  static async testChargebackRateChart(userId: string) {
    console.log('🚀 Starting Chargeback Rate Chart Performance Test');
    console.log('='.repeat(60));

    const testCases = [
      { timeRange: TimeRange.TODAY, description: 'Today (Hourly)' },
      { timeRange: TimeRange.SEVEN_DAYS, description: '7 Days (Daily)' },
      { timeRange: TimeRange.THIRTY_DAYS, description: '30 Days (Daily)' },
      { timeRange: TimeRange.THREE_MONTHS, description: '3 Months (Equal Intervals)' },
      { timeRange: TimeRange.ONE_YEAR, description: '1 Year (Monthly)' },
    ];

    for (const testCase of testCases) {
      await this.runSingleTest(userId, testCase.timeRange, testCase.description);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.printResults();
    return this.results;
  }

  /**
   * Run a single performance test
   */
  private static async runSingleTest(userId: string, timeRange: string, description: string) {
    console.log(`\n📊 Testing: ${description} (${timeRange})`);
    console.log('-'.repeat(40));

    try {
      const startTime = performance.now();
      
      const result = await MatchedBlocksService.getChargebackRateChart(userId, {
        timeRange: timeRange
      });

      const endTime = performance.now();
      const duration = endTime - startTime;
      
      let recordCount = 0;
      if (result.success && Array.isArray(result.data)) {
        recordCount = result.data.length;
      }

      console.log(`✅ Success: ${duration.toFixed(2)}ms | Records: ${recordCount}`);
      
      this.results.push({
        method: 'Prisma ORM (New)',
        timeRange: description,
        duration: parseFloat(duration.toFixed(2)),
        recordCount: recordCount
      });

    } catch (error: any) {
      console.log(`❌ Error: ${error.message}`);
      
      this.results.push({
        method: 'Prisma ORM (New)',
        timeRange: description,
        duration: 0,
        recordCount: 0,
        error: error.message
      });
    }
  }

  /**
   * Print detailed results
   */
  private static printResults() {
    console.log('\n📈 PERFORMANCE TEST RESULTS');
    console.log('='.repeat(80));
    console.log('| Time Range           | Method        | Duration (ms) | Records | Status |');
    console.log('|'.padEnd(80, '-'));

    this.results.forEach(result => {
      const timeRange = result.timeRange.padEnd(20);
      const method = result.method.padEnd(12);
      const duration = result.duration.toString().padEnd(12);
      const records = result.recordCount.toString().padEnd(7);
      const status = result.error ? 'ERROR' : 'OK';

      console.log(`| ${timeRange} | ${method} | ${duration} | ${records} | ${status}  |`);
    });

    console.log('='.repeat(80));

    // Calculate averages
    const successfulTests = this.results.filter(r => !r.error);
    if (successfulTests.length > 0) {
      const avgDuration = successfulTests.reduce((sum, r) => sum + r.duration, 0) / successfulTests.length;
      const totalRecords = successfulTests.reduce((sum, r) => sum + r.recordCount, 0);
      
      console.log(`\n📊 Summary:`);
      console.log(`   - Average Response Time: ${avgDuration.toFixed(2)}ms`);
      console.log(`   - Total Records Processed: ${totalRecords}`);
      console.log(`   - Successful Tests: ${successfulTests.length}/${this.results.length}`);
      
      // Performance indicators
      if (avgDuration < 100) {
        console.log(`   - Performance: ✅ EXCELLENT (< 100ms)`);
      } else if (avgDuration < 500) {
        console.log(`   - Performance: 🟡 GOOD (< 500ms)`);
      } else if (avgDuration < 1000) {
        console.log(`   - Performance: 🟠 FAIR (< 1s)`);
      } else {
        console.log(`   - Performance: ❌ NEEDS OPTIMIZATION (> 1s)`);
      }
    }
  }

  /**
   * Test memory usage
   */
  static measureMemoryUsage() {
    const used = process.memoryUsage();
    console.log('\n💾 Memory Usage:');
    console.log(`   - RSS: ${Math.round(used.rss / 1024 / 1024 * 100) / 100} MB`);
    console.log(`   - Heap Total: ${Math.round(used.heapTotal / 1024 / 1024 * 100) / 100} MB`);
    console.log(`   - Heap Used: ${Math.round(used.heapUsed / 1024 / 1024 * 100) / 100} MB`);
    console.log(`   - External: ${Math.round(used.external / 1024 / 1024 * 100) / 100} MB`);
  }

  /**
   * Run comprehensive performance test
   */
  static async runFullTest(userId: string = 'test-user-id') {
    console.log(`🔬 MATCHED BLOCKS CHARGEBACK DATA PERFORMANCE TEST`);
    console.log(`User ID: ${userId}`);
    console.log(`Timestamp: ${new Date().toISOString()}`);
    console.log('='.repeat(80));

    // Measure memory before test
    console.log('\n📊 Memory Usage (Before):');
    this.measureMemoryUsage();

    // Run performance tests
    await this.testChargebackRateChart(userId);

    // Measure memory after test
    console.log('\n📊 Memory Usage (After):');
    this.measureMemoryUsage();

    console.log('\n🎉 Performance test completed!');
    console.log('\n💡 Optimization Recommendations:');
    console.log('   1. Run the SQL indexes from create_performance_indexes.sql');
    console.log('   2. Consider implementing Redis caching for frequent queries');
    console.log('   3. Use background jobs for All Time calculations');
    console.log('   4. Implement query result pagination for large datasets');
  }
}

export default PerformanceTest;

// Uncomment to run the test directly
// PerformanceTest.runFullTest('your-test-user-id');