/**
 * Store IDs Performance Test
 * Test optimized linkedStore query performance
 */

import { performance } from 'perf_hooks';
import PrismaService from './prisma.service';

const prisma = PrismaService.getInstance().getClient();

class StoreIdsPerformanceTest {
  /**
   * Optimized method: Get store IDs from linkedStore table directly
   */
  private static async getUserStoreIds(userId: string): Promise<string[]> {
    const linkedStores = await prisma.linkedStore.findMany({
      where: { 
        userId,
        isActive: true // Only get active stores
      },
      select: { id: true },
    });

    return linkedStores.map(store => store.id);
  }

  /**
   * Quick performance test
   */
  static async testPerformance(userId: string, iterations: number = 5) {
    console.log(`🔬 Store IDs Performance Test`);
    console.log('='.repeat(50));
    console.log(`User ID: ${userId}`);
    console.log(`Iterations: ${iterations}\n`);
    
    const results: number[] = [];
    
    try {
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        const storeIds = await this.getUserStoreIds(userId);
        const duration = performance.now() - start;
        
        results.push(duration);
        console.log(`Run ${i + 1}: ${duration.toFixed(2)}ms | ${storeIds.length} stores`);
        
        if (i === 0) {
          console.log(`Store IDs: [${storeIds.slice(0, 3).join(', ')}${storeIds.length > 3 ? '...' : ''}]`);
        }
      }
      
      const avgTime = results.reduce((sum, r) => sum + r, 0) / results.length;
      const minTime = Math.min(...results);
      const maxTime = Math.max(...results);
      
      console.log('\n📊 Results:');
      console.log(`   Average: ${avgTime.toFixed(2)}ms`);
      console.log(`   Min: ${minTime.toFixed(2)}ms`);
      console.log(`   Max: ${maxTime.toFixed(2)}ms`);
      console.log(`   ✅ Performance: ${avgTime < 10 ? 'Excellent' : avgTime < 50 ? 'Good' : 'Needs optimization'}`);
      
    } catch (error: any) {
      console.log(`❌ Test failed: ${error.message}`);
    }
  }
}

export default StoreIdsPerformanceTest;

// Uncomment to run test
// StoreIdsPerformanceTest.testPerformance('your-user-id-here');