#!/bin/bash

echo "🚀 Applying Performance Indexes for Chargeback Data"
echo "=================================================="

# Set error handling
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Step 1: Generating Prisma client...${NC}"
npx prisma generate

echo -e "${YELLOW}Step 2: Applying database migration...${NC}"
npx prisma migrate deploy

echo -e "${YELLOW}Step 3: Checking migration status...${NC}"
npx prisma migrate status

echo -e "${YELLOW}Step 4: Testing indexes (if database is accessible)...${NC}"
if command -v psql &> /dev/null; then
    if [ ! -z "$DATABASE_URL" ]; then
        echo "Testing indexes with PostgreSQL..."
        psql "$DATABASE_URL" -f test-indexes.sql
    else
        echo -e "${RED}DATABASE_URL not set, skipping index tests${NC}"
    fi
else
    echo -e "${YELLOW}psql not found, skipping index tests${NC}"
fi

echo -e "${GREEN}✅ Performance indexes applied successfully!${NC}"
echo ""
echo "📊 Expected Performance Improvements:"
echo "  - getUserStoreIds(): 60-80% faster"
echo "  - Chargeback queries: 70-85% faster"  
echo "  - Daily/Monthly aggregations: 50-70% faster"
echo ""
echo "💡 Next Steps:"
echo "  1. Run performance tests with your actual data"
echo "  2. Monitor query performance with EXPLAIN ANALYZE"
echo "  3. Check index usage statistics after production usage"
echo ""
echo "🧪 To test performance:"
echo "  cd src/services"
echo "  node -e \"require('./performance-test.ts').default.runFullTest('your-user-id')\""