-- Performance optimization indexes for chargeback data queries
-- Run these indexes to improve query performance significantly

-- Shopify Transactions indexes
CREATE INDEX IF NOT EXISTS idx_shopify_transactions_store_created 
ON shopify_transactions(linked_store_id, created_at);

CREATE INDEX IF NOT EXISTS idx_shopify_transactions_created_store 
ON shopify_transactions(created_at, linked_store_id);

-- Shopify Disputes indexes for chargeback type filtering
CREATE INDEX IF NOT EXISTS idx_shopify_disputes_store_created_type 
ON shopify_disputes(linked_store_id, created_at, type) 
WHERE type = 'chargeback';

CREATE INDEX IF NOT EXISTS idx_shopify_disputes_created_store_type 
ON shopify_disputes(created_at, linked_store_id, type) 
WHERE type = 'chargeback';

-- Matched Blocks indexes for user store lookup
CREATE INDEX IF NOT EXISTS idx_matched_blocks_user_store 
ON matched_blocks(user_id, linked_store_id);

-- Additional covering index for better performance
CREATE INDEX IF NOT EXISTS idx_matched_blocks_user_store_covering 
ON matched_blocks(user_id) INCLUDE (linked_store_id) 
WHERE linked_store_id IS NOT NULL;